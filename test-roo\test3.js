console.log('This is test3.js');

rclone mount alist_main:/alipan/mtphoto /mnt/alist_sdb2  --vfs-cache-mode writes --allow-other  & 


/mnt/alist_sdb2/mt_photos/config
/mnt/alist_sdb2/mt_photos/upload

/mnt/sdb1/mt_photos/config
/mnt/sdb1/mt_photos/upload
/mnt/sdb1/mt_photos/upload
/mnt/sdb1/mt_photos/config

docker run -d \
  --name="mt-photos" \
  -v /mnt/alist_sdb2/mt_photos/config:/config \
  -v /mnt/alist_sdb2/mt_photos/upload:/upload \
  -p 8063:8063 \
  -e TZ="Asia/Shanghai" \
  --restart="unless-stopped" \
  mtphotos/mt-photos:latest

  fusermount -u /mnt/alist_sdb2