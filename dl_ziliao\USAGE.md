# 快速使用指南

## 🚀 三种使用方式

### 方式1: 从项目根目录运行（推荐）
```bash
# 测试下载（下载前2个文件验证）
npm run test-download

# 交互式下载（可自定义设置）
npm run download-interactive

# 直接下载（使用默认配置）
npm run download
```

### 方式2: 进入 dl_ziliao 目录运行
```bash
cd dl_ziliao

# 测试下载
node test-downloader.js

# 交互式下载
node download.js

# 直接下载
node batch-downloader.js
```

### 方式3: 自定义配置
```bash
cd dl_ziliao

# 1. 编辑配置文件
# 修改 downloader-config.json

# 2. 运行下载
node batch-downloader.js
```

## 📁 文件结构

```
dl_ziliao/
├── ziliao.json              # 数据源文件
├── batch-downloader.js      # 主下载器脚本
├── download.js              # 交互式启动脚本
├── test-downloader.js       # 测试脚本
├── downloader-config.json   # 配置文件
├── README.md               # 详细说明文档
└── USAGE.md                # 本文件
```

## ⚙️ 配置选项

编辑 `downloader-config.json` 文件：

```json
{
  "baseUrl": "https://campus.chinaunicom.cn",
  "downloadDir": "./downloads",     // 下载目录
  "maxConcurrent": 3,              // 并发数（建议2-5）
  "retryAttempts": 3,              // 重试次数
  "retryDelay": 1000               // 重试延迟(毫秒)
}
```

## 💡 使用建议

1. **首次使用**: 先运行 `npm run test-download` 测试
2. **大量文件**: 使用 `npm run download` 直接下载
3. **自定义设置**: 使用 `npm run download-interactive` 交互式配置
4. **网络不稳定**: 降低并发数到 1-2
5. **服务器限制**: 增加重试延迟到 2000-3000ms

## 🔧 故障排除

- **下载失败**: 检查网络连接和URL有效性
- **权限错误**: 确保对下载目录有写入权限
- **内存不足**: 减少并发下载数量
