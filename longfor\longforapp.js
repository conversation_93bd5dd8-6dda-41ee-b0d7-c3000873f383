/**
 * 龙湖APP自动签到脚本 - 青龙面板版
 *
 * 功能：
 * 1. 自动完成龙湖APP每日签到，获取积分
 * 2. 自动完成抽奖活动签到和抽奖
 *
 * 环境变量：
 * LONGFOR_TOKEN - 龙湖APP的token，多个账号用@或&分隔
 *
 * cron: 0 8 * * *
 * const $ = new Env('龙湖签到');
 */

const axios = require('axios');
const { sendNotify } = require('../sendNotify');

// 配置常量
const CONFIG = {
    SCRIPT_NAME: '龙湖签到',
    DEBUG_MODE: process.env.DEBUG_MODE === 'true',
    RETRY_COUNT: 3,
    RETRY_DELAY: 2000,
    REQUEST_TIMEOUT: 10000,

    // API 配置
    API: {
        SIGN_IN: "https://gw2c-hw-open.longfor.com/lmarketing-task-api-mvc-prod/openapi/task/v1/signature/clock",
        LOTTERY_SIGN: "https://gw2c-hw-open.longfor.com/llt-gateway-prod/api/v1/activity/auth/lottery/sign",
        LOTTERY_DRAW: "https://gw2c-hw-open.longfor.com/llt-gateway-prod/api/v1/activity/auth/lottery/click"
    },

    // 活动配置（易于更新）
    ACTIVITY: {
        SIGN_IN_NO: "11111111111736501868255956070000",
        LOTTERY_COMPONENT: "CO15400F29R2ZFJZ",
        LOTTERY_ACTIVITY: "AP25K062Q6YYQ7FX"
    },

    // 通用请求头 - 基于WebView的curl请求
    COMMON_HEADERS: {
        'Host': 'gw2c-hw-open.longfor.com',
        'Connection': 'keep-alive',
        'sec-ch-ua-platform': '"Android"',
        'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Android WebView";v="132"',
        'sec-ch-ua-mobile': '?1',
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json;charset=UTF-8',
        'User-Agent': 'Mozilla/5.0 (Linux; Android 15; PKG110 Build/UKQ1.231108.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/132.0.6834.163 Mobile Safari/537.36 &MAIAWebKit_android_com.longfor.supera_1.15.4_307031307_Default_3.3.1.2',
        'X-Requested-With': 'com.longfor.supera',
        'Sec-Fetch-Site': 'same-site',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Dest': 'empty',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    }
}

// 全局变量
let tokens = [];
let notifyStr = '';
let allMessage = '';

// 工具函数
function log(message, level = 'INFO') {
    const timestamp = new Date().toLocaleTimeString()
    const prefix = CONFIG.DEBUG_MODE ? `[${timestamp}][${level}] ` : ''
    console.log(`${prefix}██ ${message}`)
    allMessage += `${prefix}██ ${message}\n`
}

function logError(message, error) {
    log(`${message}: ${error}`, 'ERROR')
}

function logDebug(message) {
    if (CONFIG.DEBUG_MODE) {
        log(message, 'DEBUG')
    }
}

function isEmpty(obj) {
    return typeof obj === "undefined" || obj === null || obj === "" || obj.length === 0
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
}

function validateToken(token) {
    return !isEmpty(token) && token.length > 10
}

function sanitizeToken(token) {
    return token ? `${token.substring(0, 10)}...` : '无效token'
}

// 获取环境变量中的token
function getTokens() {
    const tokenStr = process.env.LONGFOR_TOKEN || '';
    if (!tokenStr) {
        log('未找到LONGFOR_TOKEN环境变量');
        return [];
    }

    // 支持多种分隔符
    const tokens = tokenStr.split(/[@&\n]/).filter(token => token.trim());
    log(`共找到 ${tokens.length} 个账号`);
    return tokens;
}

// HTTP请求函数（带重试机制）
async function httpPost(options, retryCount = CONFIG.RETRY_COUNT) {
    for (let attempt = 1; attempt <= retryCount; attempt++) {
        try {
            logDebug(`HTTP请求尝试 ${attempt}/${retryCount}: ${options.url}`)

            const axiosConfig = {
                method: 'POST',
                url: options.url,
                headers: options.headers,
                data: options.body,
                timeout: CONFIG.REQUEST_TIMEOUT,
                validateStatus: () => true // 不抛出HTTP错误状态异常
            }

            const response = await axios(axiosConfig)
            logDebug(`请求成功: ${JSON.stringify(response.data).substring(0, 100)}...`)

            return {
                response: response,
                data: typeof response.data === 'string' ? response.data : JSON.stringify(response.data)
            }
        } catch (error) {
            logError(`请求失败 (尝试 ${attempt})`, error.message)
            if (attempt < retryCount) {
                log(`等待 ${CONFIG.RETRY_DELAY}ms 后重试...`)
                await sleep(CONFIG.RETRY_DELAY)
            } else {
                throw new Error(`请求失败，已重试 ${retryCount} 次: ${error.message}`)
            }
        }
    }
}

// 创建请求头 - 基于WebView curl请求
function createHeaders(token, extraHeaders = {}) {
    return {
        ...CONFIG.COMMON_HEADERS,
        'X-LF-DXRisk-Source': '1',
        'X-LF-Bu-Code': 'L00502',
        'X-LF-DXRisk-Captcha-Token': 'undefined',
        'X-GAIA-API-KEY': 'c06753f1-3e68-437d-b592-b94656ea5517',
        'X-LF-UserToken': token,
        'X-LF-Channel': 'L0',
        'X-LF-DXRisk-Token': '688733a2DFOljnbdCbcwvzDOK6YsT8kdiF28CLe1',
        'token': token,
        'Origin': 'https://longzhu.longfor.com',
        'Referer': 'https://longzhu.longfor.com/',
        'Cookie': 'acw_tc=276aede417536909161991318e6a802af9e49a130a41fe14441a3203544467',
        ...extraHeaders
    }
}

// 主要功能函数
async function doLotteryCheckIn(token, accountIndex) {
    if (!validateToken(token)) {
        log(`账号${accountIndex + 1}: token无效`)
        return false
    }

    log(`账号${accountIndex + 1}: 开始执行抽奖签到，token: ${sanitizeToken(token)}`)

    try {
        // 抽奖功能使用专门的请求头格式
        const headers = {
            'Host': 'gw2c-hw-open.longfor.com',
            'Connection': 'keep-alive',
            'sec-ch-ua-platform': '"Android"',
            'X-LF-DXRisk-Source': '1',
            'authtoken': token,
            'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Android WebView";v="132"',
            'sec-ch-ua-mobile': '?1',
            'x-gaia-api-key': '2f9e3889-91d9-4684-8ff5-24d881438eaf',
            'bucode': 'L00502',
            'User-Agent': 'Mozilla/5.0 (Linux; Android 15; PKG110 Build/UKQ1.231108.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/132.0.6834.163 Mobile Safari/537.36 &MAIAWebKit_android_com.longfor.supera_1.15.4_307031307_Default_3.3.1.2',
            'Accept': 'application/json, text/plain, */*',
            'channel': 'L0',
            'Content-Type': 'application/json',
            'X-LF-DXRisk-Token': '68873cd44wz5cmc33epJN3tKF2b2QaCMhNXqool1',
            'Origin': 'https://llt.longfor.com',
            'X-Requested-With': 'com.longfor.supera',
            'Sec-Fetch-Site': 'same-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://llt.longfor.com/',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Cookie': 'acw_tc=276aedef17536933954642532e43c31601a81486a5b548b1760966befbf34e'
        }

        const signInBody = {
            "component_no": CONFIG.ACTIVITY.LOTTERY_COMPONENT,
            "activity_no": CONFIG.ACTIVITY.LOTTERY_ACTIVITY
        }

        const signInOptions = {
            url: CONFIG.API.LOTTERY_SIGN,
            headers: headers,
            body: JSON.stringify(signInBody)
        }

        log(`账号${accountIndex + 1}: 开始执行抽奖活动签到...`)
        const signInResult = await httpPost(signInOptions)
        const signInData = JSON.parse(signInResult.data)

        if (signInData.code === "0000") {
            log(`账号${accountIndex + 1}: 抽奖活动签到成功，开始执行抽奖...`)
            return await performLottery(headers, accountIndex)
        } else if (signInData.code === "863036") {
            log(`账号${accountIndex + 1}: 今日已签到，直接执行抽奖...`)
            return await performLottery(headers, accountIndex)
        } else {
            log(`账号${accountIndex + 1}: 抽奖签到返回异常: ${signInResult.data}`)
            return false
        }
    } catch (error) {
        logError(`账号${accountIndex + 1}: 抽奖签到失败`, error)
        return false
    }
}

async function performLottery(headers, accountIndex) {
    const lotteryBody = {
        "component_no": CONFIG.ACTIVITY.LOTTERY_COMPONENT,
        "activity_no": CONFIG.ACTIVITY.LOTTERY_ACTIVITY,
        "batch_no": ""
    }

    const lotteryOptions = {
        url: CONFIG.API.LOTTERY_DRAW,
        headers: headers,
        body: JSON.stringify(lotteryBody)
    }

    try {
        log(`账号${accountIndex + 1}: 开始执行抽奖...`)
        const lotteryResult = await httpPost(lotteryOptions)
        const lotteryData = JSON.parse(lotteryResult.data)

        if (lotteryData.code === "0000") {
            const prize = lotteryData.data?.prize_name || "未知奖品"
            log(`账号${accountIndex + 1}: 抽奖成功，获得奖品: ${prize}`)
            return true
        } else if (lotteryData.code === "863033") {
            log(`账号${accountIndex + 1}: 今日已抽奖`)
            return true
        } else {
            log(`账号${accountIndex + 1}: 抽奖返回异常: ${lotteryResult.data}`)
            return false
        }
    } catch (error) {
        logError(`账号${accountIndex + 1}: 抽奖失败`, error)
        return false
    }
}

async function doSignIn(token, accountIndex) {
    if (!validateToken(token)) {
        log(`账号${accountIndex + 1}: token无效`)
        return false
    }

    log(`账号${accountIndex + 1}: 开始执行签到，token: ${sanitizeToken(token)}`)

    try {
        // 签到使用WebView格式的请求头，已经在createHeaders中包含了所有必要的头部
        const headers = createHeaders(token)

        const options = {
            url: CONFIG.API.SIGN_IN,
            headers: headers,
            body: JSON.stringify({"activity_no": CONFIG.ACTIVITY.SIGN_IN_NO})
        }

        const result = await httpPost(options)
        const data = JSON.parse(result.data)

        if (data.code === 200 || data.code === "0000") {
            log(`账号${accountIndex + 1}: 签到成功: ${data.message || '获得积分'}`)
            return true
        } else {
            log(`账号${accountIndex + 1}: 签到返回异常: ${result.data}`)
            return false
        }
    } catch (error) {
        logError(`账号${accountIndex + 1}: 签到失败`, error)
        return false
    }
}

// 单个账号执行函数
async function runAccount(token, accountIndex) {
    log(`\n========== 开始执行账号${accountIndex + 1} ==========`)

    let signInSuccess = false
    let lotterySuccess = false

    try {
        // 先执行常规签到
        signInSuccess = await doSignIn(token, accountIndex)

        if (signInSuccess) {
            log(`账号${accountIndex + 1}: 常规签到完成，等待1秒后开始执行抽奖签到...`)
            await sleep(1000)
        } else {
            log(`账号${accountIndex + 1}: 常规签到失败，但仍尝试执行抽奖签到...`)
            await sleep(1000)
        }

        // 执行抽奖签到和抽奖
        lotterySuccess = await doLotteryCheckIn(token, accountIndex)

    } catch (error) {
        logError(`账号${accountIndex + 1}: 执行失败`, error)
    }

    log(`账号${accountIndex + 1}: 签到${signInSuccess ? '成功' : '失败'}，抽奖${lotterySuccess ? '成功' : '失败'}`)
    return { signInSuccess, lotterySuccess }
}

// 主执行函数
async function main() {
    log('========== 龙湖签到脚本开始执行 ==========')

    const tokens = getTokens()
    if (tokens.length === 0) {
        log('未找到有效的token，请设置LONGFOR_TOKEN环境变量')
        return
    }

    let successCount = 0
    let failCount = 0

    for (let i = 0; i < tokens.length; i++) {
        const result = await runAccount(tokens[i], i)
        if (result.signInSuccess || result.lotterySuccess) {
            successCount++
        } else {
            failCount++
        }

        // 账号间延迟
        if (i < tokens.length - 1) {
            await sleep(2000)
        }
    }

    log(`\n========== 执行完成 ==========`)
    log(`成功账号: ${successCount}，失败账号: ${failCount}`)

    // 发送通知
    if (allMessage) {
        await sendNotify(CONFIG.SCRIPT_NAME, allMessage)
    }
}

// 执行主函数
main().catch(error => {
    logError('脚本执行出错', error)
})