const axios = require('axios');
const https = require('https');
const fs = require('fs');
const dayjs = require('dayjs');
// 1. 根据wx_id获取 cookie
// 2. 添加账户
// 3. 发送答题结果

axios.defaults.timeout = 5000;

// 答题开始日期
const startDate = "2025-05-11";
// 计算当前日期距离答题开始日期的过去天数
function getDaysPassed() {
  const targetDate = dayjs(startDate);
  const currentDate = dayjs();
  const daysPassed = currentDate.diff(targetDate, 'day');
  return Math.abs(daysPassed);
}
// 获取过去的天数
const daysPassed = getDaysPassed();
console.log(`距离答题开始日期已过去${daysPassed}天`);

// 题目id
const questionId = "847";
// 正确题数
const zqsNum = 5;
// 检查提交id
const checkId = "*********";
// userList name
const userListName = "userList_minfa.json";
// 是否添加账户
const isAddAllAccounts = false;
// 当前日期
const curDate = dayjs().format("YYYYMMDD");
// 答题开始天数
const startDays = daysPassed + 1;
// 答题结束天数
const endDays = daysPassed + 1;

// 1. 根据wx_id获取 cookie
// 根据wx_id获取cookie
async function getCookie(wxId) {
  const url = `http://www.vjianghu.cn/dati1/yzdd-${questionId}.html`;
  const params = {
    wxid: wxId,
    wid: "173",
    date: curDate,
  };

  const headers = {
    Accept:
      "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "zh-CN,zh;q=0.9,en-CN;q=0.8,en;q=0.7",
    "Cache-Control": "no-cache",
    Connection: "keep-alive",
    Pragma: "no-cache",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent":
      "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Mobile Safari/537.36 MicroMessenger/7.0.20.1781(0x2700143B) NetType/WIFI Language/zh_CN",
  };

  try {
    const response = await axios.get(url, {
      params,
      headers,
      httpsAgent: new https.Agent({
        rejectUnauthorized: false,
      }),
    });

    // 从response中获取cookie
    const cookies = response.headers["set-cookie"];
    if (cookies && cookies.length > 0) {
      return cookies.join("; ");
    }
    return "";
  } catch (error) {
    console.error("获取Cookie失败:", error);
    return "";
  }
}

// 2. 添加账户
// 添加账户
async function addAccount(
  cookie,
  tel,
  un,
  wxun = "",
  city = "湖北省-潜江市-潜江市",
  id = questionId
) {
  const url = "http://www.vjianghu.cn/dati1/yzddun-add.html";

  const headers = {
    Accept: "*/*",
    "Accept-Language": "zh-CN,zh;q=0.9,en-CN;q=0.8,en;q=0.7",
    "Cache-Control": "no-cache",
    Connection: "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
    Cookie: cookie,
    Origin: "http://www.vjianghu.cn",
    Pragma: "no-cache",
    Referer: `http://www.vjianghu.cn/dati1/yzddun-${questionId}.html`,
    "User-Agent":
      "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Mobile Safari/537.36 MicroMessenger/7.0.20.1781(0x2700143B) NetType/WIFI Language/zh_CN",
    "X-Requested-With": "XMLHttpRequest",
  };

  const data = new URLSearchParams();
  data.append("tel", tel);
  data.append("un", un);
  data.append("wxun", wxun);
  data.append("city", city);
  data.append("id", id);

  try {
    const response = await axios.post(url, data, {
      headers,
      httpsAgent: new https.Agent({
        rejectUnauthorized: false,
      }),
    });
    return response.data;
  } catch (error) {
    console.error("添加账户失败:", error);
    return null;
  }
}

// 3. 发送答题结果
async function sendAnswerResult(cookie, days, zqs = zqsNum, yid = questionId) {
  const url = `http://www.vjianghu.cn/dati1/yzddkzcheck-${checkId}.html`;

  const headers = {
    "Accept-Language": "zh-CN,zh;q=0.9,en-CN;q=0.8,en;q=0.7",
    "Cache-Control": "no-cache",
    Connection: "keep-alive",
    Host: "www.vjianghu.cn",
    Origin: "http://www.vjianghu.cn",
    Pragma: "no-cache",
    Referer: `http://www.vjianghu.cn/dati1/yzddkz-${questionId}.html`,
    "User-Agent":
      "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Mobile Safari/537.36 MicroMessenger/7.0.20.1781(0x2700143B) NetType/WIFI Language/zh_CN",
    "X-Requested-With": "XMLHttpRequest",
    Cookie: cookie,
    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
  };

  const data = new URLSearchParams();
  data.append("zqs", zqs);
  data.append("days", days);
  data.append("yid", yid);

  try {
    const response = await axios.post(url, data, {
      headers,
      httpsAgent: new https.Agent({
        rejectUnauthorized: false,
      }),
    });
    return response.data;
  } catch (error) {
    console.error("发送答题结果失败:", error);
    return null;
  }
}

// sleep
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
/* 
623f906d-7b0d-4032-9d21-fc3f978a464e
07f5b3fb-8444-48ee-85bb-388d920beb4c
a5035fbc-bd4a-43e1-9f4a-8825252ebd43
*/
// http://www.vjianghu.cn/dati1/yzdd-844.html?wxid=oqFghs6d48bx9ad1VC9_8c5ea2bb&wid=173&date=250428
// 定义用户数据结构
const userList = [
  {
    phone: "13066589060",
    wxId: "oqFghs6d48bx9ad1VC9_8c5ea2bb", 
    number: "1号",
    cookie: "",
    un: "张明",
    wxun: "",
    city: "北京市-北京市-海淀区",
    id: questionId,
  },
  {
    phone: "18518174321",
    wxId: "oqFghs6d48bx9ad1VC9_okVGZ86I",
    number: "2号", 
    cookie: "",
    un: "李伟",
    wxun: "",
    city: "北京市-北京市-海淀区",
    id: questionId,
  },
  {
    phone: "18604041922",
    wxId: "oqFghs6d48bx9ad1VC9_6ddec1ae",
    number: "3号",
    cookie: "",
    un: "赵芳",
    wxun: "",
    city: "湖北省-潜江市-潜江市",
    id: questionId,
  },
  {
    phone: "13998323956", 
    wxId: "oqFghs6d48bx9ad1VC9_f3275b3e",
    number: "4号",
    cookie: "",
    un: "陈静",
    wxun: "",
    city: "天津市-天津市-和平区",
    id: questionId,
  },
  {
    phone: "15184223919",
    wxId: "oqFghs6d48bx9ad1VC9_d1782bd7",
    number: "5号",
    cookie: "",
    un: "王强",
    wxun: "",
    city: "辽宁省-沈阳市-沈河区",
    id: questionId,
  },
  {
    phone: "15524048658",
    wxId: "oqFghs6d48bx9ad1VC9_d53853cc",
    number: "6号",
    cookie: "",
    un: "刘军",
    wxun: "",
    city: "河北省-石家庄市-长安区",
    id: questionId,
  },
  {
    phone: "18525043956",
    wxId: "oqFghs6d48bx9ad1VC9_519d5617",
    number: "7号",
    cookie: "",
    un: "周杰",
    wxun: "",
    city: "湖北省-潜江市-潜江市",
    id: questionId,
  },
  {
    phone: "13842148072",
    wxId: "oqFghs6d48bx9ad1VC9_5475c0b3",
    number: "8号",
    cookie: "",
    un: "孙涛",
    wxun: "",
    city: "广东省-广州市-白云区",
    id: questionId,
  },
];

// 获取所有用户的cookie
async function initUserCookies() {
  for (const user of userList) {
    user.cookie = await getCookie(user.wxId);
    console.log(
      `${user.number}(${user.phone}) cookie获取${user.cookie ? "成功" : "失败"}`
    );
    await sleep(1000);
  }
}

// 添加所有用户的账户
async function addAllAccounts() {
  if (!isAddAllAccounts) {
    console.log("不添加账户");
    return;
  }
  for (const user of userList) {
    if (!user.cookie) {
      console.log(`${user.number}(${user.phone}) cookie为空，跳过添加账户`);
      continue;
    }
    const result = await addAccount(
      user.cookie,
      user.phone,
      user.un,
      user.wxun,
      user.city,
      user.id
    );
    console.log(`${user.number}(${user.phone}) 添加账户结果:`, result);
    await sleep(1000);
  }
}

// 发送所有用户的答题结果
async function sendAllAnswers() {
  for (const user of userList) {
    if (!user.cookie) {
      console.log(`${user.number}(${user.phone}) cookie为空，跳过答题`);
      continue;
    }
    // 开始天数
    const start = startDays;
    // 结束天数
    const end = endDays;
    for (let i = start; i <= end; i++) {
      const result = await sendAnswerResult(user.cookie, i, zqsNum, questionId);
      console.log(`${user.number}(${user.phone}) 答题结果:`, result);
      await sleep(1000);
    }
    console.log(`${user.number}(${user.phone}) 答题结束`);
  }
}

// 执行主流程
async function main() {
  await initUserCookies();
  fs.writeFileSync(userListName, JSON.stringify(userList, null, 2));
  await addAllAccounts();
  await sendAllAnswers();
}

main().catch((error) => {
  console.error("执行出错:", error);
});
