{"name": "my-scripts", "version": "1.0.0", "description": "基于 github action 的个人脚本库", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node longfor/longfor23112.js", "jdsign": "node jdsign/app.js", "pojie": "node 52pojie/app.js", "enshan": "node enshan/enshan.js", "remind": "node remind/remind.js", "juejin": "node juejin/app.js", "download": "cd dl_ziliao && node batch-downloader.js", "download-interactive": "cd dl_ziliao && node download.js", "test-download": "cd dl_ziliao && node test-downloader.js", "mail": "node mail/mail.js", "mail-test": "node mail/test-config.js", "mail-setup": "node mail/setup.js", "mail-example": "node mail/example.js"}, "repository": {"type": "git", "url": "git+https://github.com/weicracker/my-scripts.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/weicracker/my-scripts/issues"}, "homepage": "https://github.com/weicracker/my-scripts#readme", "dependencies": {"axios": "^0.21.1", "cheerio": "1.0.0-rc.12", "dayjs": "^1.11.10", "download": "^8.0.0", "iconv-lite": "^0.6.3", "node-schedule": "^2.1.1", "nodemailer": "^7.0.5", "request": "^2.88.2", "request-promise": "^4.2.5"}, "devDependencies": {"@types/node": "^18.0.0", "ts-node": "^10.9.1", "typescript": "^5.0.0"}}