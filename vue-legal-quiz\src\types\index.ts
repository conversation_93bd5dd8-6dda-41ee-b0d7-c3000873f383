// 活动状态
export type ActivityStatus = 'active' | 'ended'

// 中奖结果公布状态
export type ResultStatus = 'published' | 'unpublished'

// 活动类型
export interface Activity {
  id: string
  name: string
  description: string
  icon: string
  status: ActivityStatus
  resultStatus: ResultStatus // 中奖结果是否已公布
  winnerCount: number
  totalPrize: number
  startDate: string
  endDate: string
  color: string
  winnersFile?: string // 中奖信息文件路径（仅当resultStatus为published时有值）
}

// 中奖用户
export interface Winner {
  id: string
  name: string
  phone: string
  activityId: string
  activityName: string
  prizeAmount: number
  winTime: string
  status: 'issued' | 'pending'
}

// 搜索结果
export interface SearchResult {
  winners: Winner[]
  total: number
  totalPrize: number
}

// 搜索参数
export interface SearchParams {
  keyword: string
  activityId?: string
}

// API 响应
export interface ApiResponse<T> {
  code: number
  message: string
  data: T
}
