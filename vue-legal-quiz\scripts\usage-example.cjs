const { parseWinnerData } = require('./parseWinnerData.cjs');

/**
 * 使用示例：解析真实的中奖数据
 */

// 示例1：解析脱敏格式的中奖数据
function parseRealData() {
  // 脱敏格式的数据（包含****）
  const maskedData = `张三138****5678李四139****4321王五150****8765`;

  const winners = parseWinnerData(maskedData, '2', '民法典知识竞赛');

  console.log('解析结果：');
  winners.forEach((winner, index) => {
    console.log(`${index + 1}. ${winner.name} - ${winner.phone} - ${winner.winTime}`);
  });

  return winners;
}

// 示例2：批量处理多个活动的数据
function batchProcess() {
  const activities = [
    {
      id: '1',
      name: '2024年宪法宣传周答题活动',
      data: `张三138****5678李四139****4321` // 脱敏格式数据
    },
    {
      id: '2',
      name: '民法典知识竞赛',
      data: `王五150****8765赵六186****5432` // 脱敏格式数据
    }
  ];
  
  const allWinners = [];
  
  activities.forEach(activity => {
    const winners = parseWinnerData(activity.data, activity.id, activity.name);
    allWinners.push(...winners);
    console.log(`${activity.name}: 解析出 ${winners.length} 个中奖用户`);
  });
  
  return allWinners;
}

// 示例3：生成JSON文件
function generateJSONFile() {
  const fs = require('fs');
  const path = require('path');
  
  // 您的脱敏格式数据
  const rawData = `阿布都加帕·米吉提133****3835安栋137****0784安龙双159****5321包广金191****5161奔奔192****5986曹川135****8555陈东霞147****3294陈和平189****7089陈建华186****5979陈锦明135****1796陈开发134****2935陈磊135****6328陈敏152****4285陈翔156****0810仇如志156****3889戴翔139****9031戴运海188****1890邓广宇158****6667董永150****4189樊宗鑫130****0998冯建群191****7663冯美姣137****5364高必飞151****7371高理霞166****0206高蓉182****1189高正勇139****7981高宗帅131****2597瓜皮仔155****8280郭广新139****8257郭佳涵199****6996郭连韬150****5551郭庆文189****5568韩凤春136****8906韩景177****0711韩先飞186****6980韩仲国186****1512郝海涛151****0550郝延新139****5798何承伟138****1344何洛洛132****6647衡磊150****6430呼勇159****1190胡昌鹏139****1061胡凤其177****9134胡宇180****1140黄开宇136****7335黄美谊157****6003黄萍158****0675黄孝军150****7646火勇亮139****3855江夜袭176****1647姜涛188****3626姜懿婧139****0575蒋海峰137****7531金晨萌185****8721金和技151****6988靳138****4898老清153****5828李冰宜155****8706李博137****7888李东野150****8619李感恩155****7628李海燕139****6229李华军135****7789李建国180****7388李靖阳176****9184李林杰139****0264李世卫138****3923李向阳150****7395李宣177****0694李杨152****9300李勇进135****5195李有艮150****2215李玉婷138****5893李增斌139****7160李忠138****4030廖庆国138****7406林丹丹187****0641林嘉妮136****2174林学冬131****6081刘彩琴153****7098刘策130****6255刘国梁138****5950刘宏138****4286刘佳欣155****1357刘嘉欣137****7378刘金婷152****7871刘魁元139****4510刘茂成138****9686刘朋辉139****6720刘鹏湘153****3873刘卫卫153****2595刘夏利136****2396刘兴才130****5196刘雪梅184****2160刘彦苓152****2021刘雨轩137****4300卢为民137****7960罗家慧191****5926罗颖超139****5734吕军151****6688马磊139****9807马娜琴135****4659孟亚娜138****5349苗海涛186****9966茉莉134****1106穆湘辉133****0642宁良军139****5096潘惠玲185****6559潘啟茂181****6691蒲秀彪186****3298乔建生173****8278屈159****1278屈歌150****1986瞿祖林150****8021饶红186****5051任春生138****4628任哲曦180****4796石红新150****6224苏延豹137****3688隋玉军130****6778孙法子181****9370唐云艳136****1545田永斌158****4298佟新岳135****2081汪佳渊139****6040汪小雯137****7092王红义137****7356王辉189****6798王金瑞150****2815王军奇187****9103王立君181****6855王强春189****8886王清华189****0319王伟139****5421王伟186****2269王文霞139****6676王小波150****0798王晓梅150****0318王晓婷187****5323王兴俊135****2980韦丽萍159****8327吴永平139****0171伍开平135****5898向兵136****2456肖保文150****5012谢松华189****5928徐建慧133****7907徐静151****3934徐亮152****3175徐润哲199****3617徐少鹏155****1017徐晓宇186****3614许国栋137****7965闫孟涛136****2751严日137****2285杨帆133****8993杨纪收136****1186杨建威156****6609杨磊182****5987杨咪咪183****9706杨珊136****0037杨喜东153****2395杨洋139****6038姚丽187****2421姚燕燕130****5754叶旺186****7935易波150****3900游家军139****7399于胜涛189****7017余志翔138****2083袁波138****0197岳泰159****0922张超颖180****5964张海195****0536张海静159****5651张浩哲155****5781张华琴139****7297张靖139****5084张珂158****9455张露瑶189****3611张素平133****6361张陶133****7108张文锋186****9217张燕芳181****9820张燕玲186****0207张子健180****7234张祖民136****5018章桂花136****9316赵永江133****3151赵云江137****3582周伦明135****3240周永奇139****2966朱洁155****2438朱女士137****8512訾续东139****8026邹宝成137****3646邹海东152****1188邹蓉139****6683邹钰麟156****6247`;
  
  const winners = parseWinnerData(rawData, '2', '民法典知识竞赛');
  
  // 生成JSON文件
  const outputPath = path.join(__dirname, '../public/data/winners-activity-2.json');
  
  // 确保目录存在
  const dir = path.dirname(outputPath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  fs.writeFileSync(outputPath, JSON.stringify(winners, null, 2), 'utf8');
  
  console.log(`已生成 ${winners.length} 个中奖用户数据到: ${outputPath}`);
  
  return winners;
}

// 示例4：处理您提供的具体数据格式
function parseYourData() {
  // 您提供的脱敏格式数据（包含****）
  const yourData = `邹宝成137****3646邹海东152****1188邹蓉139****6683邹钰麟156****6247`;

  const winners = parseWinnerData(yourData, '2', '民法典知识竞赛');

  console.log('您的数据解析结果：');
  console.log(JSON.stringify(winners, null, 2));

  return winners;
}

// 运行示例
console.log('=== 中奖数据解析示例 ===\n');

console.log('1. 解析真实数据示例：');
// parseRealData();

console.log('\n2. 批量处理示例：');
// batchProcess();

console.log('\n3. 您的数据格式示例：');
// parseYourData();

console.log('\n4. 生成JSON文件示例：');
generateJSONFile(); // 取消注释以生成文件

console.log('\n=== 使用说明 ===');
console.log('1. 直接使用脱敏格式的手机号数据（包含****的格式）');
console.log('2. 调用 parseWinnerData(数据, 活动ID, 活动名称) 进行解析');
console.log('3. 解析结果可以直接用于Vue项目的JSON数据文件');
console.log('4. 每个中奖用户包含：id, name, phone, activityId, activityName, prizeAmount, winTime, status');
console.log('5. 手机号会保持脱敏格式（如：138****5678）');
