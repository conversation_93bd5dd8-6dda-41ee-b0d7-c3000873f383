const axios = require('axios')
const sendMessage = require('../utils/sendmsg')
const iconv = require('iconv-lite')

// 获取外部变量
const NSH_COOKIE = process.env.NSH_COOKIE

let headersRef = {
    'Accept': ' text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
    'Accept-Encoding': ' gzip, deflate, br',
    'Accept-Language': ' zh-CN,zh;q=0.9,en-CN;q=0.8,en;q=0.7',
    'Connection': ' keep-alive',
    'Cookie': '',
    'Host': ' www.right.com.cn',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua': '"Chromium";v="92", " Not A;Brand";v="99", "Google Chrome";v="92"',
    'Sec-Fetch-Dest': ' document',
    'Sec-Fetch-Mode': ' navigate',
    'Sec-Fetch-Site': ' none',
    'Sec-Fetch-User': ' ?1',
    'Upgrade-Insecure-Requests': ' 1',
    'User-Agent': ' Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36'
}

function initAxios() {
    const instance = axios.create({
        withCredentials: true,
        responseType: 'arraybuffer',
    });
    instance.interceptors.response.use(function (response) {
        response.data = iconv.decode(response.data, 'gbk')
        return response;
    }, function (error) {
        return Promise.reject(error);
    });
    return instance
}

async function start() {
    if (NSH_COOKIE) {
        headersRef['Cookie'] = NSH_COOKIE
        let myaxios = initAxios(headersRef)
        // 发起签到任务
        let config2 = {
            method: 'get',
            url: 'http://www.right.com.cn/forum/',
            headers: headersRef
        };
        
        let ret = await myaxios(config2)

        let retMsg = ''
        if (ret.data.search('威震天01') > -1) {
            retMsg = '签到成功'
        } else {
            retMsg = '签到失败'
        }
        sendMessage(`恩山签到：\n${retMsg}`)
    } else {
        let errmsg = '请提前配置【NSH_COOKIE】'
        console.log(errmsg)
        sendMessage(`恩山签到：\n${errmsg}`)
    }
}

start()