# 微信骰子Hook脚本 - Android轻量级版本

## 📋 脚本简介

这是一个专为Android微信设计的骰子Hook脚本，采用轻量级实现方式，最大程度减少对微信的性能影响，避免卡死问题。

**主要特性：**
- ✅ 支持用户交互式输入点数序列
- ✅ 支持多种输入格式（点号、逗号、空格分隔）
- ✅ 循环序列控制，可设置复杂的点数模式
- ✅ 轻量级Hook实现，避免微信卡死
- ✅ 完整的错误处理和输入验证
- ✅ 实时日志输出，便于调试

## 🚀 使用方法

### 1. 启动脚本
```bash
frida -U -f com.tencent.mm -l android-dice-simple2.js --no-pause
```

### 2. 设置点数序列
脚本启动后会自动弹出输入对话框，您可以输入想要的点数序列：

**支持的输入格式：**
- **单个点数**：`6` （固定6点）
- **点号分隔**：`1.2.3` （1点→2点→3点循环）
- **逗号分隔**：`1,2,3` （1点→2点→3点循环）
- **空格分隔**：`1 2 3` （1点→2点→3点循环）

**输入示例：**
- `1.2` - 1点和2点交替
- `6.6.6.1` - 三次6点后一次1点
- `1,2,3,4,5,6` - 顺序循环1-6点
- `5` - 固定5点

### 3. 操作说明
- 点击"确定"：应用新的点数序列
- 点击"取消"：保持当前序列不变
- 输入为空：保持当前序列不变

## 📊 工作原理

### Hook机制
脚本通过Hook `java.util.Random.nextInt(6)` 方法来控制骰子点数：
- 检测到参数为6的调用（骰子相关）
- 返回预设序列中的点数（0-5对应1-6点）
- 自动循环到序列下一个位置

### 序列循环
```
设置序列：[1, 2, 3]
第1次投骰：1点
第2次投骰：2点  
第3次投骰：3点
第4次投骰：1点（循环开始）
第5次投骰：2点
...
```

## 🔧 技术特性

### 轻量级设计
- 最小化Hook范围，只处理骰子相关调用
- 减少日志输出频率（每10次才输出一次）
- 快速检查机制，避免不必要的处理

### 错误处理
- 输入验证：只接受1-6范围内的点数
- 环境检查：验证Java环境和dialog对象可用性
- 异常捕获：完整的try-catch错误处理

### 兼容性
- 保留原有的内部函数接口
- 支持多种输入分隔符
- 向后兼容旧版本的核心逻辑

## 📝 日志说明

脚本运行时会输出详细的日志信息：

```
[时间] 📱 Android微信骰子Hook启动 (轻量级版本)
[时间] ✅ Java环境正常
[时间] 🚀 开始Hook Random.nextInt...
[时间] ✅ Random Hook成功
[时间] 🎉 初始化完成！
[时间] 🎮 弹出点数设置对话框...
[时间] ✅ 设置骰子序列: [1, 2, 3]
[时间] 🎯 用户设置序列成功: [1, 2, 3]
[时间] 🎲 骰子控制: 1点 (序列第1次调用)
```

## ⚠️ 注意事项

1. **仅供学习研究使用**，请勿用于任何违法违规活动
2. **环境要求**：需要root权限的Android设备和Frida环境
3. **兼容性**：主要针对Android版微信，其他版本可能需要调整
4. **性能影响**：已优化为轻量级实现，但仍可能对微信性能有轻微影响
5. **风险提示**：使用Hook工具存在被检测风险，请谨慎使用

## 🔄 版本更新

### v2.0 (当前版本)
- ✅ 新增dialog.input交互式输入功能
- ✅ 支持多种输入格式（点号、逗号、空格分隔）
- ✅ 移除全局函数导出，改为纯交互模式
- ✅ 优化用户体验和错误处理
- ✅ 更新帮助文档和使用说明

### v1.0 (旧版本)
- 基础Hook功能
- 全局函数导出控制
- 命令行操作模式

## 📞 技术支持

如遇到问题，请检查：
1. Frida环境是否正确安装
2. 设备是否已获得root权限
3. 微信版本是否兼容
4. 输入的点数序列是否符合格式要求

---

**免责声明**：本脚本仅供技术学习和研究使用，使用者需自行承担使用风险。
