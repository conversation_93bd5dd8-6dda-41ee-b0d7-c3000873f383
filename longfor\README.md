# 龙湖APP自动签到脚本 - 青龙面板版

## 功能说明
1. 自动完成龙湖APP每日签到，获取积分
2. 自动完成抽奖活动签到和抽奖
3. 支持多账号批量执行

## 环境要求
- Node.js 环境
- 青龙面板
- axios 依赖包
- sendNotify 通知模块

## 安装依赖
```bash
npm install axios
```

## 环境变量配置
在青龙面板中设置以下环境变量：

| 变量名 | 说明 | 必填 | 示例 |
|--------|------|------|------|
| LONGFOR_TOKEN | 龙湖APP的token | 是 | token1@token2&token3 |
| DEBUG_MODE | 调试模式 | 否 | true |

### 多账号配置
- 多个token用 `@` 或 `&` 分隔
- 例如：`token1@token2&token3`

## 定时任务设置
建议cron表达式：`0 8 * * *` （每天早上8点执行）

## 使用方法
1. 将 `longforapp.js` 上传到青龙面板
2. 配置环境变量 `LONGFOR_TOKEN`
3. 添加定时任务
4. 启动任务

## 获取Token方法
1. 打开龙湖APP并登录
2. 使用抓包工具（如Charles、Fiddler）
3. 在请求头中找到 `lmToken` 字段
4. 复制token值到环境变量中

## 注意事项
- Token有效期有限，需要定期更新
- 建议不要设置过于频繁的执行时间
- 多账号执行时会自动添加延迟避免频繁请求

## 日志说明
- 脚本会输出详细的执行日志
- 支持调试模式，设置 `DEBUG_MODE=true` 查看更多信息
- 执行完成后会发送通知汇总

## 常见问题
1. **Token无效**：重新获取token并更新环境变量
2. **签到失败**：检查网络连接和token有效性
3. **抽奖失败**：可能是活动已结束或已达到抽奖次数限制

## 更新日志
- v2.0: 适配青龙面板，支持多账号
- v1.0: 原始代理工具版本
