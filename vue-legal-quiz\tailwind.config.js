/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'legal-red': {
          500: '#dc2626',
          600: '#b91c1c',
        },
        'legal-gold': {
          400: '#fbbf24',
          500: '#f59e0b',
        },
        'xiaohongshu': {
          500: '#ff2442',
          600: '#ff6b6b',
        }
      },
      fontFamily: {
        'sans': ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
      },
      borderRadius: {
        '12': '12px',
        '16': '16px',
        '20': '20px',
      }
    },
  },
  plugins: [],
}
