/* 
获取活动信息
*/
const axios = require('axios');
const cheerio = require('cheerio');
const dayjs = require('dayjs');
const send = require('../sendNotify.js')

// http://legalinfo.moj.gov.cn/pub/sfbzhfx/zhfxpfxx/pfxxzthd/

async function getActive() {
    const url = 'http://legalinfo.moj.gov.cn/pub/sfbzhfx/zhfxpfxx/pfxxzthd/';
    const response = await axios.get(url);
    const $ = cheerio.load(response.data);
    const today = dayjs();
    const todayStr = today.format('YYYY-MM-DD');
    
    const activities = [];
    const list = $('.rightSide_list li').slice(0,3);
    
    list.each((i, el) => {
        const title = $(el).find('a').text().trim();
        const link = $(el).find('a').attr('href');
        const dateText = $(el).find('span').text().trim();
        
        // 方案1: 使用可选链操作符和默认值 (推荐)
        const dateMatch = dateText.match(/\[(.*?)\]/);
        const date = dateMatch?.[1] || dateText; // 如果匹配失败，使用原始dateText
        
        // 调试信息
        /* console.log('原始日期文本:', dateText);
        console.log('提取后日期:', date); */
        
        const dateStr = date.trim().replace(/\[|\]/g,'').replace(/\s+/g,'');
        console.log('活动日期:',dateStr,'活动标题:',title);
        if(dateStr === todayStr) {
            activities.push({
                title,
                link,
                date:dateStr
            });
        }
    });

    if(activities.length > 0) {
        let msg = '今日新活动:\n';
        activities.forEach(act => {
            msg += `${act.title}\n链接:${act.link}\n发布日期:${act.date}\n\n`;
        });
        send.sendNotify('普法活动提醒', msg.trim());
    }
}

// 执行
getActive();
