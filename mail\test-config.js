const EmailSender = require('./mail.js');

/**
 * 配置测试脚本
 * 用于验证邮件配置是否正确，不实际发送邮件
 */
async function testConfiguration() {
    console.log('🔍 开始测试邮件配置...');
    console.log('='.repeat(50));

    const emailSender = new EmailSender();
    
    // 检查配置
    console.log('📋 当前配置信息:');
    console.log(`发送者邮箱: ${emailSender.config.sender.email}`);
    console.log(`发送者姓名: ${emailSender.config.sender.name}`);
    console.log(`接收者邮箱: ${emailSender.config.recipient.email}`);
    console.log(`接收者姓名: ${emailSender.config.recipient.name}`);
    console.log(`SMTP服务器: ${emailSender.config.smtp.host}:${emailSender.config.smtp.port}`);
    
    // 检查授权码
    if (!emailSender.config.sender.password) {
        console.log('❌ 错误: 未设置发送者邮箱授权码');
        console.log('请设置 SENDER_PASSWORD 环境变量或在 .env 文件中配置');
        return false;
    } else {
        console.log('✅ 授权码已设置');
    }
    
    console.log('\n🔗 测试SMTP连接...');
    
    try {
        const isConnected = await emailSender.initTransporter();
        
        if (isConnected) {
            console.log('✅ SMTP连接测试成功！');
            console.log('📧 配置正确，可以发送邮件');
            return true;
        } else {
            console.log('❌ SMTP连接测试失败');
            return false;
        }
    } catch (error) {
        console.error('❌ 连接测试出错:', error.message);
        return false;
    } finally {
        emailSender.close();
    }
}

/**
 * 主函数
 */
async function main() {
    const success = await testConfiguration();
    
    console.log('\n' + '='.repeat(50));
    
    if (success) {
        console.log('🎉 配置测试通过！');
        console.log('💡 提示: 运行 "node mail/mail.js" 发送测试邮件');
    } else {
        console.log('💥 配置测试失败！');
        console.log('💡 提示: 请检查配置并参考 README.md 文档');
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    main();
}
