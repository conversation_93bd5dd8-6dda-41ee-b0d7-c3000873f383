// 测试
// 导入所需模块
const { exec } = require('child_process');
const path = require('path');

// 获取当前脚本所在目录
const scriptDir = __dirname;

// 定义要执行的脚本路径
const getDaanScript = path.join(scriptDir, 'getDaan.js');
const mainPyScript = path.join(scriptDir, 'main.py');

console.log(`[${new Date().toLocaleString()}] 开始执行自动发布任务测试...`);

// 测试执行命令的函数
function executeCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`执行命令出错: ${error}`);
        console.error(`错误输出: ${stderr}`);
        reject(error);
        return;
      }
      
      console.log(`命令输出: ${stdout}`);
      resolve(stdout);
    });
  });
}

// 执行测试
async function runTest() {
  try {
    // 先执行 getDaan.js 获取答案
    console.log(`[${new Date().toLocaleString()}] 执行 getDaan.js 获取答案...`);
    await executeCommand(`node "${getDaanScript}"`);
    
    // 然后执行 main.py 发布笔记
    console.log(`[${new Date().toLocaleString()}] 执行 main.py 发布笔记...`);
    await executeCommand(`uv run "${mainPyScript}"`);
    
    console.log(`[${new Date().toLocaleString()}] 自动发布任务测试完成！`);
  } catch (error) {
    console.error(`[${new Date().toLocaleString()}] 执行任务测试出错:`, error);
  }
}

// 运行测试
runTest();
