import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Activity, Winner, SearchResult, SearchParams } from '@/types'
import { mockActivities, mockWinners } from '@/data/mock'

export const useActivityStore = defineStore('activity', () => {
  // 状态
  const activities = ref<Activity[]>([])
  const winners = ref<Winner[]>([])
  const loading = ref(false)
  const searchLoading = ref(false)

  // 计算属性
  const activeActivities = computed(() =>
    activities.value.filter(activity => activity.status === 'active')
  )

  const endedActivities = computed(() =>
    activities.value.filter(activity => activity.status === 'ended')
  )

  // 已公布中奖结果的活动（可以查询中奖名单）
  const publishedActivities = computed(() =>
    activities.value.filter(activity => activity.resultStatus === 'published')
  )

  // 未公布中奖结果的活动
  const unpublishedActivities = computed(() =>
    activities.value.filter(activity => activity.resultStatus === 'unpublished')
  )

  // 方法
  const fetchActivities = async () => {
    loading.value = true
    try {
      const response = await fetch('/data/activities.json')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()
      activities.value = data
    } catch (error) {
      console.error('获取活动列表失败:', error)
      // 如果fetch失败，使用mock数据作为备用
      activities.value = mockActivities
    } finally {
      loading.value = false
    }
  }

  const getActivityById = (id: string): Activity | undefined => {
    return activities.value.find(activity => activity.id === id)
  }

  const getWinnersByActivity = async (activityId: string): Promise<Winner[]> => {
    loading.value = true
    try {
      // 检查活动是否已公布中奖结果
      const activity = getActivityById(activityId)
      if (!activity || activity.resultStatus === 'unpublished') {
        console.log('活动中奖结果尚未公布')
        return []
      }

      // 如果活动有中奖信息文件，则通过fetch获取
      if (activity.winnersFile) {
        const response = await fetch(activity.winnersFile)
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        const winnersData = await response.json()
        return winnersData
      } else {
        // 如果没有中奖信息文件，使用mock数据作为备用
        return mockWinners.filter(winner => winner.activityId === activityId)
      }
    } catch (error) {
      console.error('获取中奖名单失败:', error)
      // 如果fetch失败，使用mock数据作为备用
      return mockWinners.filter(winner => winner.activityId === activityId)
    } finally {
      loading.value = false
    }
  }

  const searchWinners = async (params: SearchParams): Promise<SearchResult> => {
    searchLoading.value = true
    try {
      let allWinners: Winner[] = []

      // 如果指定了活动ID，只搜索该活动
      if (params.activityId) {
        const activity = getActivityById(params.activityId)
        if (activity && activity.resultStatus === 'published') {
          allWinners = await getWinnersByActivity(params.activityId)
        }
      } else {
        // 获取所有已公布结果的活动的中奖数据
        const publishedActivitiesList = publishedActivities.value
        const winnersPromises = publishedActivitiesList.map(activity =>
          getWinnersByActivity(activity.id)
        )
        const winnersArrays = await Promise.all(winnersPromises)
        allWinners = winnersArrays.flat()
      }

      // 按关键词搜索
      let filteredWinners = allWinners
      if (params.keyword) {
        const keyword = params.keyword.toLowerCase()
        filteredWinners = allWinners.filter(winner =>
          winner.name.toLowerCase().includes(keyword) ||
          winner.phone.includes(keyword)
        )
      }

      const totalPrize = filteredWinners.reduce((sum, winner) => sum + winner.prizeAmount, 0)

      return {
        winners: filteredWinners,
        total: filteredWinners.length,
        totalPrize
      }
    } catch (error) {
      console.error('搜索失败:', error)
      return {
        winners: [],
        total: 0,
        totalPrize: 0
      }
    } finally {
      searchLoading.value = false
    }
  }

  return {
    // 状态
    activities,
    winners,
    loading,
    searchLoading,

    // 计算属性
    activeActivities,
    endedActivities,
    publishedActivities,
    unpublishedActivities,

    // 方法
    fetchActivities,
    getActivityById,
    getWinnersByActivity,
    searchWinners
  }
})
