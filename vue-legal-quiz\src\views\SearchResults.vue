<template>
  <div class="min-h-screen">
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="flex items-center justify-between p-4">
        <i class="fas fa-arrow-left text-white text-lg cursor-pointer" @click="goBack"></i>
        <h1 class="text-white text-lg font-semibold">搜索结果</h1>
        <i class="fas fa-filter text-white text-lg"></i>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <i class="fas fa-search text-gray-400 mr-3"></i>
      <input 
        v-model="searchKeyword"
        type="text" 
        class="search-input" 
        :placeholder="currentKeyword"
        @keypress.enter="handleSearch"
      >
      <i 
        class="fas fa-times text-gray-400 ml-3 cursor-pointer" 
        @click="clearSearch"
      ></i>
    </div>

    <!-- 统计信息 -->
    <div class="stats-container" v-if="searchResult">
      <div>
        <div class="text-2xl font-bold text-red-600">{{ searchResult.total }}</div>
        <div class="text-xs text-gray-600">找到结果</div>
      </div>
      <div>
        <div class="text-2xl font-bold text-yellow-600">{{ searchResult.total }}</div>
        <div class="text-xs text-gray-600">中奖次数</div>
      </div>
      <div>
        <div class="text-2xl font-bold text-green-600">{{ formatAmount(searchResult.totalPrize) }}</div>
        <div class="text-xs text-gray-600">总奖金</div>
      </div>
    </div>

    <!-- 结果列表 -->
    <div class="flex-1 pb-6">
      <LoadingSpinner v-if="activityStore.searchLoading" />
      
      <!-- 有结果 -->
      <template v-else-if="searchResult && searchResult.winners.length > 0">
        <div
          v-for="winner in searchResult.winners"
          :key="winner.id"
          class="result-card cursor-pointer hover:shadow-lg transition-all duration-300"
          @click="showWinnerDetail(winner)"
        >
          <div class="activity-tag" :style="getActivityTagStyle(winner.activityId)">
            <i :class="getActivityIcon(winner.activityId)" class="mr-1"></i>
            {{ winner.activityName }}
          </div>
          
          <div class="flex items-start justify-between">
            <div class="flex items-center">
              <div class="user-avatar" :style="getAvatarStyle(winner.activityId)">
                <i class="fas fa-user text-white text-xl"></i>
              </div>
              <div>
                <h3 class="text-lg font-bold text-gray-800">{{ winner.name }}</h3>
                <p class="text-gray-600 text-sm">{{ winner.phone }}</p>
                <p class="text-xs text-gray-500 mt-1">
                  <i class="fas fa-calendar mr-1"></i>
                  中奖时间：{{ winner.winTime }}
                </p>
              </div>
            </div>
          </div>
          
          <div class="prize-info" :style="getPrizeInfoStyle(winner.activityId)">
            <div class="flex justify-center items-center">
              <div class="text-center">
                <p class="text-sm opacity-90">奖品</p>
                <p class="font-bold">
                  <i class="fas fa-gift mr-1"></i>
                  50元话费
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 相关搜索建议 -->
        <div class="suggestion-card">
          <h3 class="text-sm font-semibold text-gray-700 mb-3 flex items-center">
            <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
            相关搜索
          </h3>
          <div class="flex flex-wrap gap-2">
            <button 
              v-for="suggestion in suggestions" 
              :key="suggestion"
              class="suggestion-btn"
              @click="searchSuggestion(suggestion)"
            >
              {{ suggestion }}
            </button>
          </div>
        </div>
      </template>

      <!-- 无结果 -->
      <template v-else-if="searchResult && searchResult.winners.length === 0">
        <div class="text-center py-16 text-white">
          <div class="empty-icon mx-auto mb-6">
            <i class="fas fa-search text-white text-4xl opacity-60"></i>
          </div>
          <h2 class="text-2xl font-bold mb-4">未找到相关信息</h2>
          <p class="text-lg opacity-80 mb-2">很抱歉，没有找到"{{ currentKeyword }}"的中奖记录</p>
          <p class="text-sm opacity-60 leading-relaxed">
            请检查输入的姓名或手机号是否正确<br>
            或尝试使用其他关键词搜索
          </p>
          <button class="retry-btn mt-6" @click="clearSearch">
            <i class="fas fa-redo mr-2"></i>
            重新搜索
          </button>
        </div>
      </template>
    </div>

    <!-- 中奖详情弹窗 -->
    <div
      class="modal-overlay"
      :class="{ show: showWinnerModal }"
      @click.self="closeWinnerModal"
    >
      <div class="modal-content" v-if="selectedWinner">
        <!-- 弹窗头部 -->
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-bold text-gray-800">中奖详情</h3>
          <i
            class="fas fa-times text-gray-400 text-xl cursor-pointer hover:text-gray-600 transition-colors duration-200"
            @click="closeWinnerModal"
          ></i>
        </div>

        <!-- 用户信息 -->
        <div class="text-center mb-6">
          <div class="winner-detail-icon mx-auto mb-4">
            <i class="fas fa-user text-white text-3xl"></i>
          </div>
          <h4 class="text-xl font-bold text-gray-800 mb-2">{{ selectedWinner.name }}</h4>
          <p class="text-gray-600">{{ selectedWinner.phone }}</p>
        </div>

        <!-- 中奖信息 -->
        <div class="space-y-4 mb-6">
          <div class="flex items-center justify-between py-2 border-b border-gray-100">
            <span class="text-gray-600">中奖活动</span>
            <span class="font-semibold text-gray-800">{{ selectedWinner.activityName }}</span>
          </div>
          <div class="flex items-start justify-between py-2 border-b border-gray-100">
            <span class="text-gray-600">活动描述</span>
            <span class="font-semibold text-gray-800 text-right max-w-48">{{ getActivityDescription(selectedWinner.activityId) }}</span>
          </div>
          <div class="flex items-center justify-between py-2 border-b border-gray-100">
            <span class="text-gray-600">奖品</span>
            <span class="font-semibold text-yellow-600">
              <i class="fas fa-gift mr-1"></i>
              {{ selectedWinner.prizeAmount }}元话费
            </span>
          </div>
          <div class="flex items-center justify-between py-2">
            <span class="text-gray-600">中奖时间</span>
            <span class="font-semibold text-gray-800">{{ selectedWinner.winTime }}</span>
          </div>
        </div>

        <!-- 关闭按钮 -->
        <button
          class="w-full bg-gradient-to-r from-red-500 to-yellow-500 text-white py-3 rounded-xl font-semibold transition-all duration-300 hover:shadow-lg"
          @click="closeWinnerModal"
        >
          知道了
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useActivityStore } from '@/stores/activity'
import LoadingSpinner from '@/components/LoadingSpinner.vue'
import type { SearchResult, Winner } from '@/types'
import { formatAmount } from '@/utils/format'

const route = useRoute()
const router = useRouter()
const activityStore = useActivityStore()

const searchKeyword = ref('')
const searchResult = ref<SearchResult | null>(null)

// 弹窗相关状态
const showWinnerModal = ref(false)
const selectedWinner = ref<Winner | null>(null)

// 当前搜索关键词
const currentKeyword = computed(() => {
  return route.query.keyword as string || ''
})

// 搜索建议
const suggestions = ref(['张三丰', '138****', '宪法活动'])

// 返回上一页
const goBack = () => {
  router.back()
}

// 处理搜索
const handleSearch = async () => {
  const keyword = searchKeyword.value.trim() || currentKeyword.value
  if (keyword) {
    await performSearch(keyword)
    // 更新 URL
    router.replace({
      name: 'Search',
      query: { 
        keyword,
        activityId: route.query.activityId as string
      }
    })
  }
}

// 执行搜索
const performSearch = async (keyword: string) => {
  const params = {
    keyword,
    activityId: route.query.activityId as string
  }
  
  searchResult.value = await activityStore.searchWinners(params)
  
  // 如果没有结果，跳转到无结果页面
  if (searchResult.value.winners.length === 0) {
    router.push({
      name: 'NoResults',
      query: { keyword }
    })
  }
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  searchResult.value = null
}

// 搜索建议
const searchSuggestion = (suggestion: string) => {
  searchKeyword.value = suggestion
  handleSearch()
}

// 获取活动标签样式
const getActivityTagStyle = (activityId: string) => {
  const colors = {
    '1': 'background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
    '2': 'background: linear-gradient(135deg, #059669 0%, #047857 100%)',
    '3': 'background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
    '4': 'background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%)'
  }
  return colors[activityId as keyof typeof colors] || colors['1']
}

// 获取活动图标
const getActivityIcon = (activityId: string) => {
  const icons = {
    '1': 'fas fa-gavel',
    '2': 'fas fa-shield-alt',
    '3': 'fas fa-university',
    '4': 'fas fa-handshake'
  }
  return icons[activityId as keyof typeof icons] || icons['1']
}

// 获取头像样式
const getAvatarStyle = (activityId: string) => {
  return `background: ${getActivityTagStyle(activityId)}`
}

// 获取奖品信息样式
const getPrizeInfoStyle = (activityId: string) => {
  return getActivityTagStyle(activityId)
}

// 显示中奖用户详情
const showWinnerDetail = (winner: Winner) => {
  selectedWinner.value = winner
  showWinnerModal.value = true
}

// 关闭中奖用户详情弹窗
const closeWinnerModal = () => {
  showWinnerModal.value = false
  selectedWinner.value = null
}

// 获取活动描述
const getActivityDescription = (activityId: string) => {
  const targetActivity = activityStore.getActivityById(activityId)
  return targetActivity?.description || '暂无描述'
}

onMounted(() => {
  if (currentKeyword.value) {
    performSearch(currentKeyword.value)
  }
})
</script>

<style scoped>
.activity-tag {
  @apply text-white px-2 py-1 rounded-xl text-xs font-semibold inline-block mb-2;
}

.user-avatar {
  @apply w-12 h-12 rounded-full flex items-center justify-center mr-4;
}

.prize-info {
  @apply text-white p-3 rounded-xl mt-3;
}

.suggestion-card {
  @apply bg-white bg-opacity-90 backdrop-blur-lg mx-5 my-4 p-4 rounded-2xl;
}

.suggestion-btn {
  @apply bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm transition-colors hover:bg-gray-200;
}

.empty-icon {
  @apply w-32 h-32 bg-white bg-opacity-10 rounded-full flex items-center justify-center backdrop-blur-lg border-2 border-white border-opacity-20;
}

.retry-btn {
  @apply bg-white bg-opacity-20 backdrop-blur-lg text-white border-2 border-white border-opacity-30 px-6 py-3 rounded-full font-semibold transition-all hover:bg-opacity-30 hover:-translate-y-1;
}

.winner-detail-icon {
  @apply w-20 h-20 bg-gradient-to-r from-legal-gold-400 to-legal-gold-500 rounded-full flex items-center justify-center shadow-lg;
}
</style>
