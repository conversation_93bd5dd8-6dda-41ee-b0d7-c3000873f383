<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无查询结果</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #fbbf24 100%);
            min-height: 100vh;
        }
        .status-bar {
            height: 47px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 14px;
            font-weight: 600;
            color: white;
        }
        .search-bar {
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            margin: 16px 20px;
            padding: 12px 16px;
            border-radius: 12px;
            display: flex;
            align-items: center;
        }
        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            font-size: 16px;
            outline: none;
        }
        .empty-state {
            text-align: center;
            padding: 60px 40px;
            color: white;
        }
        .empty-icon {
            width: 120px;
            height: 120px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.2);
        }
        .suggestion-card {
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            margin: 20px;
            padding: 20px;
            border-radius: 16px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .suggestion-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .suggestion-item:hover {
            background: #e9ecef;
            transform: translateX(4px);
        }
        .retry-btn {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            margin-top: 24px;
            transition: all 0.3s ease;
        }
        .retry-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .activity-suggestion {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            display: inline-block;
            margin-left: 8px;
        }
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 24px;
            margin: 20px;
            max-width: 300px;
            width: 100%;
            text-align: center;
            transform: scale(0.8);
            transition: transform 0.3s ease;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        .modal-overlay.show .modal-content {
            transform: scale(1);
        }
        .xiaohongshu-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
        }
        .modal-btn {
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .modal-btn.secondary {
            background: #f3f4f6;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <i class="fas fa-battery-three-quarters text-sm"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg">
        <div class="flex items-center justify-between p-4">
            <i class="fas fa-arrow-left text-white text-lg"></i>
            <h1 class="text-white text-lg font-semibold">查询结果</h1>
            <i class="fas fa-redo text-white text-lg"></i>
        </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
        <i class="fas fa-search text-gray-400 mr-3"></i>
        <input type="text" class="search-input" placeholder="李明" value="李明">
        <i class="fas fa-times text-gray-400 ml-3"></i>
    </div>

    <!-- 空状态 -->
    <div class="empty-state">
        <div class="empty-icon">
            <i class="fas fa-search text-white text-4xl opacity-60"></i>
        </div>
        
        <h2 class="text-2xl font-bold mb-4">未找到相关信息</h2>
        <p class="text-lg opacity-80 mb-2">很抱歉，没有找到"李明"的中奖记录</p>
        <p class="text-sm opacity-60 leading-relaxed">
            请检查输入的姓名或手机号是否正确<br>
            或尝试在不同活动中搜索
        </p>
        
        <button class="retry-btn" onclick="clearAndRetry()">
            <i class="fas fa-redo mr-2"></i>
            重新搜索
        </button>
    </div>

    <!-- 搜索建议 -->
    <div class="suggestion-card">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
            搜索建议
        </h3>
        
        <div class="space-y-3">
            <div class="suggestion-item" onclick="searchSuggestion('张三')">
                <i class="fas fa-user text-red-500 mr-3"></i>
                <div class="flex-1">
                    <p class="font-medium text-gray-800">尝试搜索：张三</p>
                    <p class="text-sm text-gray-600">常见中奖用户</p>
                </div>
                <div class="activity-suggestion">3次中奖</div>
            </div>
            
            <div class="suggestion-item" onclick="searchSuggestion('138****5678')">
                <i class="fas fa-phone text-green-500 mr-3"></i>
                <div class="flex-1">
                    <p class="font-medium text-gray-800">尝试搜索：138****5678</p>
                    <p class="text-sm text-gray-600">手机号格式示例</p>
                </div>
                <div class="activity-suggestion">已中奖</div>
            </div>
            
            <div class="suggestion-item" onclick="searchSuggestion('王')">
                <i class="fas fa-search text-purple-500 mr-3"></i>
                <div class="flex-1">
                    <p class="font-medium text-gray-800">尝试搜索：王</p>
                    <p class="text-sm text-gray-600">使用部分姓名搜索</p>
                </div>
                <div class="activity-suggestion">模糊匹配</div>
            </div>
        </div>
    </div>

    <!-- 活动推荐 -->
    <div class="suggestion-card">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-fire text-red-500 mr-2"></i>
            热门活动
        </h3>
        
        <div class="space-y-3">
            <div class="suggestion-item" onclick="viewActivity('宪法宣传周')">
                <i class="fas fa-gavel text-red-500 mr-3"></i>
                <div class="flex-1">
                    <p class="font-medium text-gray-800">2024年宪法宣传周答题活动</p>
                    <p class="text-sm text-gray-600">156人中奖 · 进行中</p>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
            
            <div class="suggestion-item" onclick="viewActivity('民法典')">
                <i class="fas fa-shield-alt text-green-500 mr-3"></i>
                <div class="flex-1">
                    <p class="font-medium text-gray-800">民法典知识竞赛</p>
                    <p class="text-sm text-gray-600">89人中奖 · 进行中</p>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>
    </div>

    <!-- 帮助信息 -->
    <div class="suggestion-card">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-question-circle text-blue-500 mr-2"></i>
            搜索帮助
        </h3>
        
        <div class="space-y-3 text-sm text-gray-600">
            <div class="flex items-start">
                <i class="fas fa-check text-green-500 mr-3 mt-0.5"></i>
                <div>
                    <p class="font-medium text-gray-800">支持姓名搜索</p>
                    <p>输入完整姓名或部分姓名进行查询</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <i class="fas fa-check text-green-500 mr-3 mt-0.5"></i>
                <div>
                    <p class="font-medium text-gray-800">支持手机号搜索</p>
                    <p>输入完整手机号或带*号的脱敏手机号</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <i class="fas fa-check text-green-500 mr-3 mt-0.5"></i>
                <div>
                    <p class="font-medium text-gray-800">跨活动搜索</p>
                    <p>可以搜索用户在所有活动中的中奖记录</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <i class="fas fa-check text-green-500 mr-3 mt-0.5"></i>
                <div>
                    <p class="font-medium text-gray-800">实时更新</p>
                    <p>中奖信息实时同步，确保数据准确</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 小红书推广弹框 -->
    <div class="modal-overlay" id="promotionModal">
        <div class="modal-content">
            <div class="xiaohongshu-icon">
                <i class="fab fa-instagram text-white text-2xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-800 mb-2">关注小红书获取更多</h3>
            <p class="text-sm text-gray-600 mb-4">最新普法资讯 · 答题答案</p>
            <div class="flex justify-center">
                <button class="modal-btn" onclick="followXiaohongshu()">
                    <i class="fab fa-instagram mr-2"></i>
                    立即关注
                </button>
                <button class="modal-btn secondary" onclick="closeModal()">
                    稍后再说
                </button>
            </div>
        </div>
    </div>



    <script>
        // 页面加载时显示推广弹框
        window.addEventListener('load', function() {
            setTimeout(() => {
                showModal();
            }, 1000); // 1秒后显示弹框（无结果时更快显示）
        });

        // 显示弹框
        function showModal() {
            const modal = document.getElementById('promotionModal');
            modal.classList.add('show');
        }

        // 关闭弹框
        function closeModal() {
            const modal = document.getElementById('promotionModal');
            modal.classList.remove('show');
        }

        // 关注小红书
        function followXiaohongshu() {
            console.log('跳转到小红书关注页面');
            alert('即将跳转到小红书关注页面');
            closeModal();
        }

        // 点击遮罩层关闭弹框
        document.getElementById('promotionModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        function clearAndRetry() {
            const input = document.querySelector('.search-input');
            input.value = '';
            input.focus();
        }

        function searchSuggestion(query) {
            const input = document.querySelector('.search-input');
            input.value = query;
            console.log('搜索建议:', query);
        }

        function viewActivity(activity) {
            console.log('查看活动:', activity);
        }

        // 搜索功能
        document.querySelector('.search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = e.target.value.trim();
                if (query) {
                    console.log('新搜索:', query);
                }
            }
        });

        // 清除搜索
        document.querySelector('.fa-times').addEventListener('click', function() {
            clearAndRetry();
        });
    </script>
</body>
</html>
