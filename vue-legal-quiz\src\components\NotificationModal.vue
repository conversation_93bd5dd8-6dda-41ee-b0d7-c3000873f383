<template>
  <div
    ref="modalRef"
    class="modal-overlay"
    :class="{ show: isVisible }"
    @click="handleOverlayClick"
  >
    <div class="modal-content">
      <!-- 头部图标 -->
      <div class="notification-icon">
        <i class="fas fa-bell text-white text-2xl"></i>
      </div>

      <!-- 标题 -->
      <h3 class="text-lg font-bold text-gray-800 mb-2">系统通知</h3>
      <p class="text-sm text-gray-600 mb-4">{{ currentTime }}</p>

      <!-- 系统介绍 -->
      <div class="system-intro mb-4" v-if="systemIntro">
        <h4 class="text-md font-semibold text-gray-800 mb-2">{{ systemIntro.title }}</h4>
        <div class="intro-content">
          <p
            v-for="(item, index) in systemIntro.items"
            :key="index"
            class="intro-item"
          >
            <i :class="[item.icon, item.color]" class="mr-2"></i>
            <span>{{ item.text }}</span>
          </p>
        </div>
      </div>

      <!-- 最新通知 -->
      <div class="latest-notifications mb-4">
        <h4 class="text-md font-semibold text-gray-800 mb-2">🔔 最新通知</h4>
        <div class="notification-list">
          <div
            v-for="notification in latestNotifications"
            :key="notification.id"
            class="notification-item"
          >
            <div class="flex items-start">
              <i :class="notification.icon" class="text-blue-500 mr-2 mt-1 text-sm"></i>
              <div class="flex-1">
                <p class="text-gray-800 text-sm font-medium">{{ notification.title }}</p>
                <p class="text-gray-600 text-xs mt-1">{{ notification.content }}</p>
                <p class="text-gray-400 text-xs mt-1">{{ notification.time }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="flex justify-center">
        <button class="modal-btn" @click="closeModal">
          <i class="fas fa-check mr-2"></i>
          知道了
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface Notification {
  id: string
  title: string
  content: string
  time: string
  icon: string
  read: boolean
}

interface SystemIntroItem {
  icon: string
  color: string
  text: string
}

interface SystemIntro {
  title: string
  items: SystemIntroItem[]
}

interface NotificationData {
  systemIntro: SystemIntro
  latestNotifications: Notification[]
}

const modalRef = ref<HTMLElement>()
const isVisible = ref(false)

// 当前时间
const currentTime = computed(() => {
  const now = new Date()
  return `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日 ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
})

// 数据状态
const systemIntro = ref<SystemIntro | null>(null)
const latestNotifications = ref<Notification[]>([])
const loading = ref(false)

// sessionStorage 键名
const NOTIFICATION_SHOWN_KEY = 'notification_shown_this_session'

// 本次会话状态管理
const hasShownNotificationThisSession = ref(false)

// 从 sessionStorage 获取通知显示状态
const getNotificationShownStatus = (): boolean => {
  try {
    const status = sessionStorage.getItem(NOTIFICATION_SHOWN_KEY)
    return status === 'true'
  } catch (error) {
    console.error('获取通知状态失败:', error)
    return false
  }
}

// 保存通知显示状态到 sessionStorage
const saveNotificationShownStatus = (shown: boolean) => {
  try {
    sessionStorage.setItem(NOTIFICATION_SHOWN_KEY, shown.toString())
  } catch (error) {
    console.error('保存通知状态失败:', error)
  }
}

// 清除通知状态（用于测试或重置）
const clearNotificationStatus = () => {
  try {
    sessionStorage.removeItem(NOTIFICATION_SHOWN_KEY)
    hasShownNotificationThisSession.value = false
  } catch (error) {
    console.error('清除通知状态失败:', error)
  }
}

// 获取通知数据
const fetchNotifications = async () => {
  loading.value = true
  try {
    const response = await fetch('/data/notifications.json')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const data: NotificationData = await response.json()
    systemIntro.value = data.systemIntro
    latestNotifications.value = data.latestNotifications
  } catch (error) {
    console.error('获取通知数据失败:', error)
    // 如果fetch失败，使用默认数据
    systemIntro.value = {
      title: '📖 系统使用介绍',
      items: [
        {
          icon: 'fas fa-search',
          color: 'text-blue-500',
          text: '首页搜索框用于过滤活动，详情页搜索框用于查询中奖信息'
        },
        {
          icon: 'fas fa-list',
          color: 'text-green-500',
          text: '点击活动卡片查看详细中奖名单和搜索中奖用户'
        },
        {
          icon: 'fas fa-filter',
          color: 'text-orange-500',
          text: '只有已公布结果的活动才能查询中奖信息'
        },
        {
          icon: 'fas fa-gift',
          color: 'text-red-500',
          text: '所有奖品均为50元话费，中奖后自动发放'
        }
      ]
    }
    latestNotifications.value = [
      {
        id: '1',
        title: '新活动上线',
        content: '2024年宪法宣传周答题活动正在进行中',
        time: '2024-12-01',
        icon: 'fas fa-gift',
        read: false
      },
      {
        id: '2',
        title: '中奖结果公布',
        content: '民法典知识竞赛中奖名单已公布',
        time: '2024-11-30',
        icon: 'fas fa-trophy',
        read: false
      }
    ]
  } finally {
    loading.value = false
  }
}

// 显示弹框
const showModal = async () => {
  // 如果数据还没有加载，先加载数据
  if (!systemIntro.value && latestNotifications.value.length === 0) {
    await fetchNotifications()
  }
  isVisible.value = true
}

// 关闭弹框
const closeModal = () => {
  isVisible.value = false
  // 标记本次会话已显示过通知，并保存到sessionStorage
  hasShownNotificationThisSession.value = true
  saveNotificationShownStatus(true)
}

// 标记全部已读
const markAllAsRead = () => {
  latestNotifications.value.forEach(notification => {
    notification.read = true
  })
}

// 点击遮罩层关闭弹框
const handleOverlayClick = (event: MouseEvent) => {
  if (event.target === modalRef.value) {
    closeModal()
  }
}

// 初始化时加载数据
onMounted(() => {
  // 从sessionStorage初始化状态
  hasShownNotificationThisSession.value = getNotificationShownStatus()

  // 加载通知数据
  fetchNotifications()
})

// 检查是否应该显示通知（sessionStorage中没有状态）
const shouldShowNotification = () => {
  return !getNotificationShownStatus()
}

// 暴露方法给父组件
defineExpose({
  showModal,
  closeModal,
  fetchNotifications,
  shouldShowNotification,
  clearNotificationStatus
})
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center p-4 opacity-0 pointer-events-none transition-all duration-300;
}

.modal-overlay.show {
  @apply opacity-100 pointer-events-auto;
}

.modal-content {
  @apply bg-white rounded-2xl shadow-2xl max-w-sm w-full max-h-[80vh] overflow-y-auto transform scale-95 transition-transform duration-300 p-6 text-center;
}

.modal-overlay.show .modal-content {
  @apply scale-100;
}

.notification-icon {
  @apply w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-3;
}

.system-intro {
  @apply text-left bg-gray-50 rounded-lg p-4;
}

.intro-content {
  @apply space-y-2;
}

.intro-item {
  @apply flex items-start text-sm text-gray-700;
}

.latest-notifications {
  @apply text-left bg-blue-50 rounded-lg p-4;
}

.notification-list {
  @apply space-y-3;
}

.notification-item {
  @apply p-3 rounded-lg bg-white border border-gray-200;
}

.modal-btn {
  @apply bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-full font-semibold transition-all duration-200 hover:from-blue-600 hover:to-blue-700 active:scale-95;
}
</style>
