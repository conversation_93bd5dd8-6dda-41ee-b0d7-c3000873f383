/**
 * 微信骰子Hook脚本 - Android轻量级版本
 * 专门解决微信卡死问题，最小化Hook影响
 * 支持用户输入点数序列
 *
 * 使用方法：
 * frida -U -f com.tencent.mm -l android-dice-simple2.js --no-pause
 */

// 全局变量
let diceSequence = [1, 2];  // 骰子序列：1点和2点循环
let currentIndex = 0;       // 当前序列索引
let hookEnabled = true;     // Hook开关
let hookCallCount = 0;      // Hook调用计数
let diceCallCount = 0;      // 骰子调用计数（用于循环）

// 简单日志函数
function log(message) {
    const time = new Date().toLocaleTimeString();
    console.log(`[${time}] ${message}`);
}

// 获取当前应该使用的骰子点数
function getCurrentDiceValue() {
    if (!hookEnabled) {
        return 0; // 禁用时返回0表示随机
    }

    // 从序列中获取当前点数
    const currentValue = diceSequence[currentIndex];

    // 移动到下一个索引（循环）
    currentIndex = (currentIndex + 1) % diceSequence.length;

    return currentValue;
}

// 设置骰子序列
function setSequence(sequence) {
    if (Array.isArray(sequence) && sequence.length > 0) {
        // 验证序列中的所有值都在1-6范围内
        const validSequence = sequence.filter(v => v >= 1 && v <= 6);
        if (validSequence.length === sequence.length) {
            diceSequence = [...sequence];
            currentIndex = 0; // 重置索引
            log(`✅ 设置骰子序列: [${sequence.join(', ')}]`);
            return true;
        } else {
            log('❌ 错误：序列中包含无效点数，有效范围: 1-6');
            return false;
        }
    } else {
        log('❌ 错误：请提供有效的数组序列');
        return false;
    }
}

// 使用dialog.input获取用户输入的点数序列
function getUserDiceSequence() {
    try {
        dialog.input('设置TOUZI点数', {
            ok: function (res) {
                if (!res || res.trim() === '') {
                    log('❌ 输入为空，保持当前序列');
                    return;
                }

                // 解析用户输入的点数序列
                const inputStr = res.trim();
                let sequence = [];

                // 支持多种分隔符：点号、逗号、空格
                if (inputStr.includes('.')) {
                    sequence = inputStr.split('.').map(s => parseInt(s.trim())).filter(n => !isNaN(n));
                } else if (inputStr.includes(',')) {
                    sequence = inputStr.split(',').map(s => parseInt(s.trim())).filter(n => !isNaN(n));
                } else if (inputStr.includes(' ')) {
                    sequence = inputStr.split(' ').map(s => parseInt(s.trim())).filter(n => !isNaN(n));
                } else {
                    // 单个数字
                    const num = parseInt(inputStr);
                    if (!isNaN(num)) {
                        sequence = [num];
                    }
                }

                if (sequence.length === 0) {
                    log('❌ 无法解析输入的点数序列');
                    return;
                }

                // 设置新的序列
                if (setSequence(sequence)) {
                    log(`🎯 用户设置序列成功: [${sequence.join(', ')}]`);
                } else {
                    log('❌ 设置序列失败');
                }
            },
            cancel: function () {
                log('🚫 用户取消输入，保持当前序列');
            }
        }, diceSequence.join('.'));  // 默认值为当前序列

    } catch (error) {
        log('❌ 获取用户输入失败: ' + error.message);
    }
}

// 设置单个骰子点数（保持兼容性）
function setDice(value) {
    if (value >= 1 && value <= 6) {
        setSequence([value]); // 转换为单元素序列
        return true;
    } else if (value === 0) {
        hookEnabled = false;
        log('✅ 设置为随机模式');
        return true;
    } else {
        log('❌ 错误：点数必须在 0-6 之间');
        return false;
    }
}

// 切换Hook状态
function toggleHook() {
    hookEnabled = !hookEnabled;
    log(`🔧 Hook ${hookEnabled ? '已启用' : '已禁用'}`);
    return hookEnabled;
}

// 查看状态
function getStatus() {
    const info = {
        骰子序列: diceSequence,
        当前索引: currentIndex,
        下一个点数: hookEnabled ? diceSequence[currentIndex] : '随机',
        Hook状态: hookEnabled ? '启用' : '禁用',
        Hook调用次数: hookCallCount,
        骰子调用次数: diceCallCount
    };
    log('📊 当前状态: ' + JSON.stringify(info));
    return info;
}

// 显示帮助
function help() {
    console.log('\n=== 使用说明 ===');
    console.log('脚本启动后会自动弹出对话框让您输入点数序列');
    console.log('输入格式：');
    console.log('  单个点数: 6');
    console.log('  多个点数: 1.2.3 或 1,2,3 或 1 2 3');
    console.log('  有效范围: 1-6');
    console.log('\n=== 当前配置 ===');
    console.log(`骰子序列: [${diceSequence.join(', ')}]`);
    console.log(`下一个点数: ${hookEnabled ? diceSequence[currentIndex] : '随机'}`);
    console.log('================\n');
}

// 核心Hook函数 - 最小化实现
function hookDiceRandom() {
    log('🚀 开始Hook Random.nextInt...');
    
    try {
        const Random = Java.use('java.util.Random');
        
        // 保存原始方法
        const originalNextInt = Random.nextInt.overload('int');
        
        // Hook实现 - 循环序列逻辑
        Random.nextInt.overload('int').implementation = function(bound) {
            hookCallCount++;

            // 快速检查：只处理骰子相关调用
            if (hookEnabled && bound === 6) {
                diceCallCount++;

                // 获取当前应该使用的点数
                const currentDiceValue = getCurrentDiceValue();

                // 每10次骰子调用才输出一次日志，减少性能影响
                if (diceCallCount % 10 === 1) {
                    log(`🎲 骰子控制: ${currentDiceValue}点 (序列第${diceCallCount}次调用)`);
                    log(`📋 当前序列: [${diceSequence.join(', ')}], 下一个: ${diceSequence[currentIndex]}`);
                }

                return currentDiceValue - 1; // 返回0-5对应1-6点
            }

            // 其他情况直接调用原方法
            return originalNextInt.call(this, bound);
        };
        
        log('✅ Random Hook成功');
        return true;
    } catch (error) {
        log('❌ Hook失败: ' + error.message);
        return false;
    }
}

// 初始化函数
function init() {
    log('📱 Android微信骰子Hook启动 (轻量级版本)');

    // 检查Java环境
    if (!Java.available) {
        log('❌ Java环境不可用');
        return false;
    }

    log('✅ Java环境正常');

    // 执行Hook
    const success = hookDiceRandom();

    if (success) {
        log('🎉 初始化完成！');
        help();
        log(`🎯 当前设置: 序列 [${diceSequence.join(', ')}], 下一个: ${diceSequence[currentIndex]}点`);

        // 延迟弹出输入对话框，确保Hook已经完成
        setTimeout(() => {
            log('🎮 弹出点数设置对话框...');
            getUserDiceSequence();
        }, 1000);
    } else {
        log('💥 初始化失败');
    }

    return success;
}

// 注释：已移除全局函数导出功能，改为使用dialog.input交互

// 主函数
function main() {
    log('🔄 脚本加载中...');
    
    // 延迟初始化，避免过早Hook
    setTimeout(() => {
        init();
    }, 2000);
}

// 异常处理
Java.perform(function() {
    try {
        main();
    } catch (error) {
        log('💥 脚本执行异常: ' + error.message);
    }
});

log('📜 脚本已加载，等待初始化...');
