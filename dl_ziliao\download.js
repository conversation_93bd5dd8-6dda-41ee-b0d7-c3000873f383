#!/usr/bin/env node

/**
 * 快速启动脚本
 * 提供交互式界面来配置和启动下载
 */

const readline = require('readline');
const fs = require('fs').promises;
const path = require('path');
const BatchDownloader = require('./batch-downloader');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function main() {
    console.log('🚀 批量文件下载器 - 快速启动');
    console.log('================================\n');

    try {
        // 检查 JSON 文件
        const defaultJsonPath = path.join(__dirname, 'ziliao.json');
        let jsonPath = defaultJsonPath;
        
        try {
            await fs.access(defaultJsonPath);
            console.log(`✅ 找到默认 JSON 文件: ${path.basename(defaultJsonPath)}`);
        } catch {
            console.log(`❌ 未找到默认 JSON 文件: ${path.basename(defaultJsonPath)}`);
            jsonPath = await question('请输入 JSON 文件路径: ');
            
            try {
                await fs.access(jsonPath);
                console.log(`✅ JSON 文件存在: ${jsonPath}`);
            } catch {
                console.error(`❌ JSON 文件不存在: ${jsonPath}`);
                rl.close();
                return;
            }
        }

        // 读取文件信息
        const data = await fs.readFile(jsonPath, 'utf8');
        const jsonData = JSON.parse(data);
        const fileCount = jsonData.entity?.planLibList?.length || 0;
        
        console.log(`📋 找到 ${fileCount} 个文件待下载\n`);

        // 配置选项
        console.log('⚙️  配置下载选项:');
        
        const downloadDir = await question(`下载目录 [./downloads]: `) || './downloads';
        const maxConcurrentInput = await question(`最大并发数 [3]: `);
        const maxConcurrent = parseInt(maxConcurrentInput) || 3;
        
        console.log('\n📊 配置摘要:');
        console.log(`📁 下载目录: ${downloadDir}`);
        console.log(`🔄 并发数: ${maxConcurrent}`);
        console.log(`📋 文件数量: ${fileCount}`);
        
        const confirm = await question('\n确认开始下载? (y/N): ');
        
        if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
            console.log('❌ 下载已取消');
            rl.close();
            return;
        }

        rl.close();

        // 开始下载
        console.log('\n🚀 开始下载...\n');
        
        const downloader = new BatchDownloader({
            downloadDir,
            maxConcurrent,
            retryAttempts: 3,
            retryDelay: 1000
        });

        await downloader.startDownload(jsonPath);

    } catch (error) {
        console.error('❌ 错误:', error.message);
    } finally {
        rl.close();
    }
}

if (require.main === module) {
    main().catch(console.error);
}
