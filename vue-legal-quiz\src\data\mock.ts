import type { Activity, Winner } from '@/types'

export const mockActivities: Activity[] = [
  {
    id: '1',
    name: '2024年宪法宣传周答题活动',
    description: '弘扬宪法精神，建设法治中国',
    icon: 'fas fa-gavel',
    status: 'active',
    resultStatus: 'unpublished', // 进行中的活动，中奖结果未公布
    winnerCount: 2000, // 未公布结果时为0
    totalPrize: 60000, // 未公布结果时为0
    startDate: '2024-12-01',
    endDate: '2024-12-07',
    color: 'from-red-500 to-red-600'
  },
  {
    id: '2',
    name: '民法典知识竞赛',
    description: '学习民法典，守护美好生活',
    icon: 'fas fa-shield-alt',
    status: 'ended',
    resultStatus: 'published', // 已结束且公布了中奖结果
    winnerCount: 89,
    totalPrize: 4450,
    startDate: '2024-11-15',
    endDate: '2024-11-30',
    color: 'from-green-500 to-green-600'
  },
  {
    id: '3',
    name: '法治政府建设知识问答',
    description: '推进法治政府建设',
    icon: 'fas fa-university',
    status: 'ended',
    resultStatus: 'published', // 已结束且公布了中奖结果
    winnerCount: 234,
    totalPrize: 11700,
    startDate: '2024-10-01',
    endDate: '2024-10-31',
    color: 'from-gray-500 to-gray-600'
  },
  {
    id: '4',
    name: '劳动法律知识竞答',
    description: '维护劳动者合法权益',
    icon: 'fas fa-handshake',
    status: 'ended',
    resultStatus: 'unpublished', // 已结束但未公布中奖结果
    winnerCount: 0, // 未公布结果时为0
    totalPrize: 0, // 未公布结果时为0
    startDate: '2024-09-01',
    endDate: '2024-09-30',
    color: 'from-purple-500 to-purple-600'
  },
  {
    id: '5',
    name: '消费者权益保护法答题',
    description: '维护消费者合法权益',
    icon: 'fas fa-shopping-cart',
    status: 'ended',
    resultStatus: 'published', // 已结束且公布了中奖结果
    winnerCount: 156,
    totalPrize: 7800,
    startDate: '2024-08-01',
    endDate: '2024-08-31',
    color: 'from-blue-500 to-blue-600'
  }
]

export const mockWinners: Winner[] = [
  // 只有已公布结果的活动才有中奖记录

  // 活动2 - 民法典知识竞赛 (已公布结果)
  {
    id: '1',
    name: '张三',
    phone: '138****5678',
    activityId: '2',
    activityName: '民法典知识竞赛',
    prizeAmount: 50,
    winTime: '2024-11-28 16:45',
    status: 'issued'
  },
  {
    id: '2',
    name: '李四',
    phone: '139****1234',
    activityId: '2',
    activityName: '民法典知识竞赛',
    prizeAmount: 50,
    winTime: '2024-11-29 10:30',
    status: 'issued'
  },
  {
    id: '3',
    name: '王五',
    phone: '137****9876',
    activityId: '2',
    activityName: '民法典知识竞赛',
    prizeAmount: 50,
    winTime: '2024-11-29 14:20',
    status: 'issued'
  },
  {
    id: '4',
    name: '赵六',
    phone: '136****4321',
    activityId: '2',
    activityName: '民法典知识竞赛',
    prizeAmount: 50,
    winTime: '2024-11-29 16:15',
    status: 'issued'
  },
  {
    id: '5',
    name: '孙七',
    phone: '135****7890',
    activityId: '2',
    activityName: '民法典知识竞赛',
    prizeAmount: 50,
    winTime: '2024-11-30 09:30',
    status: 'issued'
  },

  // 活动3 - 法治政府建设知识问答 (已公布结果)
  {
    id: '6',
    name: '张三',
    phone: '138****5678',
    activityId: '3',
    activityName: '法治政府建设知识问答',
    prizeAmount: 50,
    winTime: '2024-10-15 10:20',
    status: 'issued'
  },
  {
    id: '7',
    name: '赵六',
    phone: '135****4567',
    activityId: '3',
    activityName: '法治政府建设知识问答',
    prizeAmount: 50,
    winTime: '2024-10-16 15:30',
    status: 'issued'
  },
  {
    id: '8',
    name: '孙七',
    phone: '136****8901',
    activityId: '3',
    activityName: '法治政府建设知识问答',
    prizeAmount: 50,
    winTime: '2024-10-17 09:45',
    status: 'issued'
  },
  {
    id: '9',
    name: '周八',
    phone: '137****2468',
    activityId: '3',
    activityName: '法治政府建设知识问答',
    prizeAmount: 50,
    winTime: '2024-10-18 14:20',
    status: 'issued'
  },
  {
    id: '10',
    name: '吴九',
    phone: '139****3579',
    activityId: '3',
    activityName: '法治政府建设知识问答',
    prizeAmount: 50,
    winTime: '2024-10-19 11:10',
    status: 'issued'
  },

  // 活动4 - 交通安全法规学习 (已公布结果)
  {
    id: '11',
    name: '郑十',
    phone: '131****4680',
    activityId: '4',
    activityName: '交通安全法规学习',
    prizeAmount: 50,
    winTime: '2024-09-15 10:30',
    status: 'issued'
  },
  {
    id: '12',
    name: '王五',
    phone: '137****9876',
    activityId: '4',
    activityName: '交通安全法规学习',
    prizeAmount: 50,
    winTime: '2024-09-16 15:45',
    status: 'issued'
  },
  {
    id: '13',
    name: '李四',
    phone: '139****1234',
    activityId: '4',
    activityName: '交通安全法规学习',
    prizeAmount: 50,
    winTime: '2024-09-17 09:20',
    status: 'issued'
  },

  // 活动5 - 消费者权益保护法答题 (已公布结果)
  {
    id: '14',
    name: '钱八',
    phone: '133****2468',
    activityId: '5',
    activityName: '消费者权益保护法答题',
    prizeAmount: 50,
    winTime: '2024-08-25 11:15',
    status: 'issued'
  },
  {
    id: '15',
    name: '张三',
    phone: '138****5678',
    activityId: '5',
    activityName: '消费者权益保护法答题',
    prizeAmount: 50,
    winTime: '2024-08-26 16:40',
    status: 'issued'
  },
  {
    id: '16',
    name: '周九',
    phone: '134****1357',
    activityId: '5',
    activityName: '消费者权益保护法答题',
    prizeAmount: 50,
    winTime: '2024-08-27 13:25',
    status: 'issued'
  },
  {
    id: '17',
    name: '陈十一',
    phone: '132****9753',
    activityId: '5',
    activityName: '消费者权益保护法答题',
    prizeAmount: 50,
    winTime: '2024-08-28 10:50',
    status: 'issued'
  },

  // 活动6 - 劳动法知识竞答 (已公布结果)
  {
    id: '18',
    name: '林十二',
    phone: '130****8642',
    activityId: '6',
    activityName: '劳动法知识竞答',
    prizeAmount: 50,
    winTime: '2024-07-20 14:30',
    status: 'issued'
  },
  {
    id: '19',
    name: '张三',
    phone: '138****5678',
    activityId: '6',
    activityName: '劳动法知识竞答',
    prizeAmount: 50,
    winTime: '2024-07-21 16:15',
    status: 'issued'
  },
  {
    id: '20',
    name: '黄十三',
    phone: '138****7531',
    activityId: '6',
    activityName: '劳动法知识竞答',
    prizeAmount: 50,
    winTime: '2024-07-22 11:40',
    status: 'issued'
  }
]
