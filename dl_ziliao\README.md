# 批量文件下载器

这是一个基于 Node.js 的批量文件下载工具，专门用于从 `ziliao.json` 文件中读取文件列表并批量下载。

## 功能特性

- ✅ **批量下载**: 从 JSON 文件读取文件列表并批量下载
- ✅ **并发控制**: 支持限制并发下载数量，避免服务器压力
- ✅ **错误处理**: 包含重试机制和详细的错误信息
- ✅ **进度显示**: 实时显示下载进度和文件大小
- ✅ **文件检查**: 自动跳过已存在的文件
- ✅ **安全文件名**: 自动处理文件名中的特殊字符
- ✅ **统计信息**: 显示下载完成的详细统计

## 快速开始

### 1. 测试下载器（推荐首次使用）
```bash
cd dl_ziliao
node test-downloader.js
```
这会下载前2个文件进行测试，确保一切正常工作。

### 2. 交互式下载（推荐）
```bash
cd dl_ziliao
node download.js
```
提供友好的交互界面，可以自定义下载目录和并发数。

### 3. 直接下载（使用默认配置）
```bash
cd dl_ziliao
node batch-downloader.js
```
使用默认配置直接开始下载所有文件。

### 4. 自定义配置下载
编辑 `downloader-config.json` 文件，然后运行：
```bash
cd dl_ziliao
node batch-downloader.js
```

## 配置说明

### downloader-config.json 配置项：

```json
{
  "baseUrl": "https://campus.chinaunicom.cn/training/image/",  // 基础URL
  "downloadDir": "./downloads",                // 下载目录
  "maxConcurrent": 3,                         // 最大并发下载数
  "retryAttempts": 3,                         // 失败重试次数
  "retryDelay": 1000,                         // 重试延迟(毫秒)
  "timeout": 30000,                           // 下载超时时间(毫秒)
  "skipExisting": true,                       // 跳过已存在文件
  "createSubdirs": false,                     // 是否创建子目录
  "logLevel": "info"                          // 日志级别
}
```

## 文件说明

- `batch-downloader.js` - 主下载器脚本
- `download.js` - 交互式启动脚本
- `test-downloader.js` - 测试脚本
- `downloader-config.json` - 配置文件
- `ziliao.json` - 数据源文件

## 使用场景

### 场景1: 首次使用
```bash
cd dl_ziliao

# 1. 先测试
node test-downloader.js

# 2. 如果测试成功，使用交互式下载
node download.js
```

### 场景2: 批量下载大量文件
```bash
cd dl_ziliao

# 1. 编辑配置文件，设置合适的并发数
# 2. 直接下载
node batch-downloader.js
```

### 场景3: 自定义下载目录
```bash
cd dl_ziliao

# 使用交互式下载，可以指定目录
node download.js
```

## 输出示例

```
🚀 开始批量下载任务...
📂 JSON 文件: ziliao.json
📁 下载目录: ./downloads
🔄 最大并发数: 3

📋 找到 8 个文件待下载

📥 开始下载 (1/3): 1-2025年客户运营部署工作落地培训.pdf
📊 1-2025年客户运营部署工作落地培训.pdf: 45.2% (1.2MB/2.7MB)
✅ 下载完成: 1-2025年客户运营部署工作落地培训.pdf

⏭️  跳过已存在的文件: 2-策略运营实战训练（北方）.pdf

📊 下载统计:
总文件数: 8
✅ 成功下载: 7
⏭️  跳过文件: 1
❌ 下载失败: 0

🎉 所有文件下载完成！
```

## 常见问题

### Q: 下载失败怎么办？
A: 脚本会自动重试3次。如果仍然失败，检查：
- 网络连接是否正常
- JSON文件中的URL是否有效
- 是否有足够的磁盘空间

### Q: 如何暂停和恢复下载？
A: 使用 Ctrl+C 停止下载。重新运行脚本时会自动跳过已下载的文件。

### Q: 如何修改并发数？
A: 
- 方法1: 使用交互式下载 `node download.js`
- 方法2: 编辑 `downloader-config.json` 文件

### Q: 下载的文件在哪里？
A: 默认在 `./downloads` 目录，可以通过配置文件或交互式界面修改。

## 性能建议

- **并发数**: 建议设置为 2-5，过高可能导致服务器拒绝连接
- **网络**: 确保网络连接稳定，脚本会处理临时网络问题
- **磁盘**: 确保有足够的磁盘空间，PDF文件通常较大

## 依赖要求

- Node.js >= 12.0.0
- 无需额外安装依赖包（使用 Node.js 内置模块）

## 故障排除

### 下载失败
- 检查网络连接
- 验证 JSON 文件格式是否正确
- 确认文件 URL 是否有效

### 权限错误
- 检查下载目录的写入权限
- 在 Windows 上可能需要以管理员身份运行

### 内存不足
- 减少并发下载数量
- 确保系统有足够的可用内存
