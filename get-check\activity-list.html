<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>普法答题活动列表</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #fbbf24 100%);
            min-height: 100vh;
        }
        .status-bar {
            height: 47px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 14px;
            font-weight: 600;
            color: white;
        }
        .activity-card {
            background: white;
            border-radius: 16px;
            margin: 16px 20px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-left: 4px solid #dc2626;
            transition: all 0.3s ease;
        }
        .activity-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        .search-bar {
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            margin: 16px 20px;
            padding: 12px 16px;
            border-radius: 12px;
            display: flex;
            align-items: center;
        }
        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            font-size: 16px;
            outline: none;
        }
        .activity-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
        }
        .status-active {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }
        .status-ended {
            background: #f3f4f6;
            color: #6b7280;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
            margin-top: 16px;
        }
        .stat-item {
            text-align: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 24px;
            margin: 20px;
            max-width: 300px;
            width: 100%;
            text-align: center;
            transform: scale(0.8);
            transition: transform 0.3s ease;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        .modal-overlay.show .modal-content {
            transform: scale(1);
        }
        .xiaohongshu-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
        }
        .modal-btn {
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .modal-btn.secondary {
            background: #f3f4f6;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <i class="fas fa-battery-three-quarters text-sm"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg">
        <div class="flex items-center justify-between p-4">
            <div class="w-6"></div>
            <h1 class="text-white text-lg font-semibold flex items-center">
                <i class="fas fa-balance-scale mr-2"></i>
                普法答题活动
            </h1>
            <i class="fas fa-bell text-white text-lg"></i>
        </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
        <i class="fas fa-search text-gray-400 mr-3"></i>
        <input type="text" class="search-input" placeholder="搜索活动名称或关键词">
        <i class="fas fa-filter text-gray-400 ml-3"></i>
    </div>

    <!-- 活动列表 -->
    <div class="flex-1 pb-6">
        <!-- 活动1 - 进行中 -->
        <div class="activity-card">
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center">
                    <div class="activity-icon">
                        <i class="fas fa-gavel text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-800">2024年宪法宣传周答题活动</h3>
                        <p class="text-gray-600 text-sm">弘扬宪法精神，建设法治中国</p>
                    </div>
                </div>
                <div class="status-badge status-active">
                    <i class="fas fa-play mr-1"></i>
                    进行中
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="text-xl font-bold text-red-600">156</div>
                    <div class="text-xs text-gray-600">中奖人数</div>
                </div>
                <div class="stat-item">
                    <div class="text-xl font-bold text-yellow-600">¥7800</div>
                    <div class="text-xs text-gray-600">总奖金</div>
                </div>
                <div class="stat-item">
                    <div class="text-xl font-bold text-green-600">进行中</div>
                    <div class="text-xs text-gray-600">活动状态</div>
                </div>
            </div>
            
            <div class="mt-4 pt-4 border-t border-gray-200">
                <p class="text-xs text-gray-500 flex items-center">
                    <i class="fas fa-calendar mr-2"></i>
                    活动时间：2024-12-01 至 2024-12-07
                </p>
            </div>
        </div>

        <!-- 活动2 - 进行中 -->
        <div class="activity-card">
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center">
                    <div class="activity-icon" style="background: linear-gradient(135deg, #059669 0%, #047857 100%);">
                        <i class="fas fa-shield-alt text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-800">民法典知识竞赛</h3>
                        <p class="text-gray-600 text-sm">学习民法典，守护美好生活</p>
                    </div>
                </div>
                <div class="status-badge status-active">
                    <i class="fas fa-play mr-1"></i>
                    进行中
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="text-xl font-bold text-red-600">89</div>
                    <div class="text-xs text-gray-600">中奖人数</div>
                </div>
                <div class="stat-item">
                    <div class="text-xl font-bold text-yellow-600">¥4450</div>
                    <div class="text-xs text-gray-600">总奖金</div>
                </div>
                <div class="stat-item">
                    <div class="text-xl font-bold text-green-600">进行中</div>
                    <div class="text-xs text-gray-600">活动状态</div>
                </div>
            </div>
            
            <div class="mt-4 pt-4 border-t border-gray-200">
                <p class="text-xs text-gray-500 flex items-center">
                    <i class="fas fa-calendar mr-2"></i>
                    活动时间：2024-11-15 至 2024-12-15
                </p>
            </div>
        </div>

        <!-- 活动3 - 已结束 -->
        <div class="activity-card" style="border-left-color: #6b7280;">
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center">
                    <div class="activity-icon" style="background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);">
                        <i class="fas fa-university text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-800">法治政府建设知识问答</h3>
                        <p class="text-gray-600 text-sm">推进法治政府建设</p>
                    </div>
                </div>
                <div class="status-badge status-ended">
                    <i class="fas fa-check mr-1"></i>
                    已结束
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="text-xl font-bold text-red-600">234</div>
                    <div class="text-xs text-gray-600">中奖人数</div>
                </div>
                <div class="stat-item">
                    <div class="text-xl font-bold text-yellow-600">¥11700</div>
                    <div class="text-xs text-gray-600">总奖金</div>
                </div>
                <div class="stat-item">
                    <div class="text-xl font-bold text-gray-600">已结束</div>
                    <div class="text-xs text-gray-600">活动状态</div>
                </div>
            </div>
            
            <div class="mt-4 pt-4 border-t border-gray-200">
                <p class="text-xs text-gray-500 flex items-center">
                    <i class="fas fa-calendar mr-2"></i>
                    活动时间：2024-10-01 至 2024-10-31
                </p>
            </div>
        </div>

        <!-- 活动4 - 已结束 -->
        <div class="activity-card" style="border-left-color: #6b7280;">
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center">
                    <div class="activity-icon" style="background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);">
                        <i class="fas fa-handshake text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-800">劳动法律知识竞答</h3>
                        <p class="text-gray-600 text-sm">维护劳动者合法权益</p>
                    </div>
                </div>
                <div class="status-badge status-ended">
                    <i class="fas fa-check mr-1"></i>
                    已结束
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="text-xl font-bold text-red-600">178</div>
                    <div class="text-xs text-gray-600">中奖人数</div>
                </div>
                <div class="stat-item">
                    <div class="text-xl font-bold text-yellow-600">¥8900</div>
                    <div class="text-xs text-gray-600">总奖金</div>
                </div>
                <div class="stat-item">
                    <div class="text-xl font-bold text-gray-600">已结束</div>
                    <div class="text-xs text-gray-600">活动状态</div>
                </div>
            </div>
            
            <div class="mt-4 pt-4 border-t border-gray-200">
                <p class="text-xs text-gray-500 flex items-center">
                    <i class="fas fa-calendar mr-2"></i>
                    活动时间：2024-09-01 至 2024-09-30
                </p>
            </div>
        </div>
    </div>

    <!-- 小红书推广弹框 -->
    <div class="modal-overlay" id="promotionModal">
        <div class="modal-content">
            <div class="xiaohongshu-icon">
                <i class="fab fa-instagram text-white text-2xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-800 mb-2">关注小红书获取更多</h3>
            <p class="text-sm text-gray-600 mb-4">最新普法资讯 · 答题答案</p>
            <div class="flex justify-center">
                <button class="modal-btn" onclick="followXiaohongshu()">
                    <i class="fab fa-instagram mr-2"></i>
                    立即关注
                </button>
                <button class="modal-btn secondary" onclick="closeModal()">
                    稍后再说
                </button>
            </div>
        </div>
    </div>



    <script>
        // 页面加载时显示推广弹框
        window.addEventListener('load', function() {
            setTimeout(() => {
                showModal();
            }, 1000); // 1秒后显示弹框
        });

        // 显示弹框
        function showModal() {
            const modal = document.getElementById('promotionModal');
            modal.classList.add('show');
        }

        // 关闭弹框
        function closeModal() {
            const modal = document.getElementById('promotionModal');
            modal.classList.remove('show');
        }

        // 关注小红书
        function followXiaohongshu() {
            console.log('跳转到小红书关注页面');
            alert('即将跳转到小红书关注页面');
            closeModal();
        }

        // 点击遮罩层关闭弹框
        document.getElementById('promotionModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 搜索功能
        document.querySelector('.search-input').addEventListener('input', function(e) {
            console.log('搜索活动:', e.target.value);
        });

        // 活动卡片点击
        document.querySelectorAll('.activity-card').forEach(card => {
            card.addEventListener('click', function() {
                console.log('点击活动:', this.querySelector('h3').textContent);
            });
        });
    </script>
</body>
</html>
