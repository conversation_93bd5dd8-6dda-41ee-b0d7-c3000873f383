{"version": "0.2.0", "configurations": [{"type": "pwa-node", "request": "launch", "name": "longfor Program", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/longfor/app.js"}, {"type": "pwa-node", "request": "launch", "name": "jdsign Program", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/jdsign/app.js"}, {"type": "pwa-node", "request": "launch", "name": "52pojie Program", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/52pojie/app.js", "env": {}}, {"type": "pwa-node", "request": "launch", "name": "enshan Program", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/enshan/enshan.js", "env": {}}, {"type": "pwa-node", "request": "launch", "name": "remind Program", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/remind/remind.js", "env": {}}, {"type": "pwa-node", "request": "launch", "name": "juejin Program", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/juejin/app.js", "env": {}}]}