[Unit]
Description=rclone mount for OneDrive (optimized)
After=network-online.target
Requires=network-online.target
AssertPathIsDirectory=/mnt/oned_sdb3

[Service]
Type=notify
User=root
ExecStart=/usr/bin/rclone mount \
    oned:/ /mnt/oned_sdb3 \
    --config=/root/.config/rclone/rclone.conf \
    --allow-other \
    --vfs-cache-mode full \
    --vfs-read-chunk-size 32M \
    --buffer-size 64M \
    --dir-cache-time 72h \
    --umask 022 \  # 文件权限 755（目录）和 644（文件）
    --default-permissions \
    --log-file=/var/log/rclone.log \
    --log-level INFO
ExecStop=/bin/fusermount -uz /mnt/oned_sdb3
Restart=on-failure
RestartSec=10
MemoryHigh=400M
MemoryMax=1G

[Install]
WantedBy=multi-user.target