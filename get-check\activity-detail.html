<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宪法宣传周答题活动</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #fbbf24 100%);
            min-height: 100vh;
        }
        .status-bar {
            height: 47px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 14px;
            font-weight: 600;
            color: white;
        }
        .winner-card {
            background: white;
            border-radius: 16px;
            margin: 8px 20px;
            padding: 16px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 4px solid #fbbf24;
        }
        .search-bar {
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            margin: 16px 20px;
            padding: 12px 16px;
            border-radius: 12px;
            display: flex;
            align-items: center;
        }
        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            font-size: 16px;
            outline: none;
        }
        .activity-header {
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            margin: 16px 20px;
            padding: 20px;
            border-radius: 16px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .stats-container {
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            margin: 16px 20px;
            padding: 16px;
            border-radius: 12px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
            text-align: center;
        }
        .winner-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
        .prize-badge {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
        }
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 24px;
            margin: 20px;
            max-width: 300px;
            width: 100%;
            text-align: center;
            transform: scale(0.8);
            transition: transform 0.3s ease;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        .modal-overlay.show .modal-content {
            transform: scale(1);
        }
        .xiaohongshu-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
        }
        .modal-btn {
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .modal-btn.secondary {
            background: #f3f4f6;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <i class="fas fa-battery-three-quarters text-sm"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg">
        <div class="flex items-center justify-between p-4">
            <i class="fas fa-arrow-left text-white text-lg"></i>
            <h1 class="text-white text-lg font-semibold">活动详情</h1>
            <i class="fas fa-share-alt text-white text-lg"></i>
        </div>
    </div>

    <!-- 活动信息 -->
    <div class="activity-header">
        <div class="flex items-center mb-4">
            <div class="w-16 h-16 bg-gradient-to-r from-red-500 to-yellow-500 rounded-full flex items-center justify-center mr-4">
                <i class="fas fa-gavel text-white text-2xl"></i>
            </div>
            <div>
                <h2 class="text-xl font-bold text-gray-800">2024年宪法宣传周答题活动</h2>
                <p class="text-gray-600 text-sm">弘扬宪法精神，建设法治中国</p>
                <div class="mt-2">
                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                        <i class="fas fa-play mr-1"></i>进行中
                    </span>
                </div>
            </div>
        </div>
        <div class="text-sm text-gray-600">
            <p class="flex items-center mb-1">
                <i class="fas fa-calendar mr-2 text-red-500"></i>
                活动时间：2024-12-01 至 2024-12-07
            </p>
            <p class="flex items-center">
                <i class="fas fa-gift mr-2 text-yellow-500"></i>
                奖品：50元话费（每人）
            </p>
        </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
        <i class="fas fa-search text-gray-400 mr-3"></i>
        <input type="text" class="search-input" placeholder="搜索中奖用户姓名或手机号">
        <i class="fas fa-times text-gray-400 ml-3"></i>
    </div>

    <!-- 统计信息 -->
    <div class="stats-container">
        <div>
            <div class="text-2xl font-bold text-red-600">156</div>
            <div class="text-xs text-gray-600">中奖人数</div>
        </div>
        <div>
            <div class="text-2xl font-bold text-yellow-600">¥7800</div>
            <div class="text-xs text-gray-600">总奖金</div>
        </div>
        <div>
            <div class="text-2xl font-bold text-green-600">进行中</div>
            <div class="text-xs text-gray-600">活动状态</div>
        </div>
    </div>

    <!-- 中奖名单 -->
    <div class="flex-1 pb-6">
        <!-- 中奖用户1 -->
        <div class="winner-card">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="winner-icon">
                        <i class="fas fa-user text-white text-lg"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-gray-800">张三</h3>
                        <p class="text-gray-600 text-sm">138****5678</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="prize-badge">
                        <i class="fas fa-gift mr-1"></i>
                        50元话费
                    </div>
                    <p class="text-xs text-gray-500 mt-1">12-03 14:30</p>
                </div>
            </div>
        </div>

        <!-- 中奖用户2 -->
        <div class="winner-card">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="winner-icon">
                        <i class="fas fa-user text-white text-lg"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-gray-800">李四</h3>
                        <p class="text-gray-600 text-sm">139****1234</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="prize-badge">
                        <i class="fas fa-gift mr-1"></i>
                        50元话费
                    </div>
                    <p class="text-xs text-gray-500 mt-1">12-03 15:45</p>
                </div>
            </div>
        </div>

        <!-- 中奖用户3 -->
        <div class="winner-card">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="winner-icon">
                        <i class="fas fa-user text-white text-lg"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-gray-800">王五</h3>
                        <p class="text-gray-600 text-sm">137****9876</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="prize-badge">
                        <i class="fas fa-gift mr-1"></i>
                        50元话费
                    </div>
                    <p class="text-xs text-gray-500 mt-1">12-03 16:20</p>
                </div>
            </div>
        </div>

        <!-- 中奖用户4 -->
        <div class="winner-card">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="winner-icon">
                        <i class="fas fa-user text-white text-lg"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-gray-800">赵六</h3>
                        <p class="text-gray-600 text-sm">135****4567</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="prize-badge">
                        <i class="fas fa-gift mr-1"></i>
                        50元话费
                    </div>
                    <p class="text-xs text-gray-500 mt-1">12-03 17:10</p>
                </div>
            </div>
        </div>

        <!-- 中奖用户5 -->
        <div class="winner-card">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="winner-icon">
                        <i class="fas fa-user text-white text-lg"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-gray-800">孙七</h3>
                        <p class="text-gray-600 text-sm">136****8901</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="prize-badge">
                        <i class="fas fa-gift mr-1"></i>
                        50元话费
                    </div>
                    <p class="text-xs text-gray-500 mt-1">12-03 18:25</p>
                </div>
            </div>
        </div>

        <!-- 加载更多提示 -->
        <div class="text-center py-6">
            <button class="bg-white bg-opacity-20 backdrop-filter backdrop-blur-lg text-white px-6 py-3 rounded-full border border-white border-opacity-30">
                <i class="fas fa-chevron-down mr-2"></i>
                加载更多中奖用户
            </button>
        </div>
    </div>

    <!-- 小红书推广弹框 -->
    <div class="modal-overlay" id="promotionModal">
        <div class="modal-content">
            <div class="xiaohongshu-icon">
                <i class="fab fa-instagram text-white text-2xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-800 mb-2">关注小红书获取更多</h3>
            <p class="text-sm text-gray-600 mb-4">最新普法资讯 · 答题答案</p>
            <div class="flex justify-center">
                <button class="modal-btn" onclick="followXiaohongshu()">
                    <i class="fab fa-instagram mr-2"></i>
                    立即关注
                </button>
                <button class="modal-btn secondary" onclick="closeModal()">
                    稍后再说
                </button>
            </div>
        </div>
    </div>



    <script>
        // 页面加载时显示推广弹框
        window.addEventListener('load', function() {
            setTimeout(() => {
                showModal();
            }, 1500); // 1.5秒后显示弹框
        });

        // 显示弹框
        function showModal() {
            const modal = document.getElementById('promotionModal');
            modal.classList.add('show');
        }

        // 关闭弹框
        function closeModal() {
            const modal = document.getElementById('promotionModal');
            modal.classList.remove('show');
        }

        // 关注小红书
        function followXiaohongshu() {
            console.log('跳转到小红书关注页面');
            alert('即将跳转到小红书关注页面');
            closeModal();
        }

        // 点击遮罩层关闭弹框
        document.getElementById('promotionModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 搜索功能
        document.querySelector('.search-input').addEventListener('input', function(e) {
            console.log('搜索中奖用户:', e.target.value);
        });

        // 清除搜索
        document.querySelector('.fa-times').addEventListener('click', function() {
            document.querySelector('.search-input').value = '';
        });

        // 加载更多
        document.querySelector('button').addEventListener('click', function() {
            console.log('加载更多中奖用户');
        });
    </script>
</body>
</html>
