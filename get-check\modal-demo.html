<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹框演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #fbbf24 100%);
            min-height: 100vh;
        }
        .status-bar {
            height: 47px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 14px;
            font-weight: 600;
            color: white;
        }
        .demo-content {
            text-align: center;
            padding: 60px 20px;
            color: white;
        }
        .demo-btn {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            padding: 16px 32px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            margin: 16px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .demo-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 24px;
            margin: 20px;
            max-width: 300px;
            width: 100%;
            text-align: center;
            transform: scale(0.8);
            transition: transform 0.3s ease;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        .modal-overlay.show .modal-content {
            transform: scale(1);
        }
        .xiaohongshu-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
        }
        .modal-btn {
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .modal-btn.secondary {
            background: #f3f4f6;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <i class="fas fa-battery-three-quarters text-sm"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg">
        <div class="flex items-center justify-between p-4">
            <i class="fas fa-arrow-left text-white text-lg"></i>
            <h1 class="text-white text-lg font-semibold">弹框演示</h1>
            <i class="fas fa-cog text-white text-lg"></i>
        </div>
    </div>

    <!-- 演示内容 -->
    <div class="demo-content">
        <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-magic text-white text-2xl"></i>
        </div>
        <h2 class="text-2xl font-bold mb-4">小红书推广弹框演示</h2>
        <p class="text-lg opacity-90 mb-6">点击按钮查看弹框效果</p>
        
        <button class="demo-btn" onclick="showModal()">
            <i class="fas fa-eye mr-2"></i>
            显示推广弹框
        </button>
        
        <div class="mt-8 text-sm opacity-75">
            <p>• 弹框会在页面加载后自动显示</p>
            <p>• 用户可以选择关注或稍后再说</p>
            <p>• 点击遮罩层可关闭弹框</p>
        </div>
    </div>

    <!-- 小红书推广弹框 -->
    <div class="modal-overlay" id="promotionModal">
        <div class="modal-content">
            <div class="xiaohongshu-icon">
                <i class="fab fa-instagram text-white text-2xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-800 mb-2">关注小红书获取更多</h3>
            <p class="text-sm text-gray-600 mb-4">最新普法资讯 · 答题答案</p>
            <div class="flex justify-center">
                <button class="modal-btn" onclick="followXiaohongshu()">
                    <i class="fab fa-instagram mr-2"></i>
                    立即关注
                </button>
                <button class="modal-btn secondary" onclick="closeModal()">
                    稍后再说
                </button>
            </div>
        </div>
    </div>



    <script>
        // 页面加载时自动显示推广弹框（演示用）
        window.addEventListener('load', function() {
            setTimeout(() => {
                showModal();
            }, 2000); // 2秒后自动显示弹框
        });
        
        // 显示弹框
        function showModal() {
            const modal = document.getElementById('promotionModal');
            modal.classList.add('show');
        }
        
        // 关闭弹框
        function closeModal() {
            const modal = document.getElementById('promotionModal');
            modal.classList.remove('show');
        }
        
        // 关注小红书
        function followXiaohongshu() {
            console.log('跳转到小红书关注页面');
            alert('即将跳转到小红书关注页面');
            closeModal();
        }
        
        // 点击遮罩层关闭弹框
        document.getElementById('promotionModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
