name: longfor dayliy auto card 
on: 
  # push: # 推送触发
  # workflow_dispatch:
  # schedule:
  #   - cron:  '33 4 * * *'

jobs:
  Claim:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          persist-credentials: false
      - name: Use Node.js 14.x 
        uses: actions/setup-node@v1 
        with:
          node-version: "14.x"
                  
      - name: Run
        run: |
          npm i 
          npm start
        env:
          LF_USERTOKEN: ${{ secrets.LF_USERTOKEN }}