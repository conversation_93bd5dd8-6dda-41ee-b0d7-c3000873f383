// --- START OF FILE custom-features.js ---

/**
 * 自定义Augment扩展功能模块
 * 专门用于管理 accessToken 和 tenantURL
 */

const vscode = require('vscode');
const crypto = require('crypto');

// 常量定义，避免魔法字符串
const SESSIONS_KEY = 'augment.sessions';
const SESSION_ID_KEY = 'sessionId';

class AugmentCustomFeatures {
    constructor() {
        this.context = null;
        this.outputChannel = null;
        this.logger = console; // 初始默认，将在 initialize 中被完全替换
        this.isInitialized = false;
    }

    /**
     * 初始化自定义功能
     * @param {vscode.ExtensionContext} context 扩展上下文
     */
    async initialize(context) {
        if (this.isInitialized) {
            this.logger.warn('Custom features already initialized');
            return;
        }

        this.context = context;

        // --- 核心改造：创建复合日志记录器 ---

        // 1. 始终创建 OutputChannel 和一个基础的日志记录器
        this.outputChannel = vscode.window.createOutputChannel('Augment Custom');
        const outputChannelLogger = {
            info: (msg, ...args) => this.outputChannel.appendLine(`[INFO] ${msg} ${args.length > 0 ? JSON.stringify(args) : ''}`),
            warn: (msg, ...args) => this.outputChannel.appendLine(`[WARN] ${msg} ${args.length > 0 ? JSON.stringify(args) : ''}`),
            error: (msg, ...args) => this.outputChannel.appendLine(`[ERROR] ${msg} ${args.length > 0 ? JSON.stringify(args) : ''}`),
            debug: (msg, ...args) => this.outputChannel.appendLine(`[DEBUG] ${msg} ${args.length > 0 ? JSON.stringify(args) : ''}`)
        };

        // 2. 根据环境模式决定最终的 this.logger
        if (context.extensionMode !== vscode.ExtensionMode.Production) {
            // 开发模式: 创建一个复合 logger，同时输出到 OutputChannel 和 Console
            this.logger = {
                info: (msg, ...args) => {
                    const prefix = '[Augment Custom DEV]';
                    outputChannelLogger.info(msg, ...args);
                    console.info(`${prefix} ${msg}`, ...args);
                },
                warn: (msg, ...args) => {
                    const prefix = '[Augment Custom DEV]';
                    outputChannelLogger.warn(msg, ...args);
                    console.warn(`${prefix} ${msg}`, ...args);
                },
                error: (msg, ...args) => {
                    const prefix = '[Augment Custom DEV]';
                    outputChannelLogger.error(msg, ...args);
                    console.error(`${prefix} ${msg}`, ...args);
                },
                debug: (msg, ...args) => {
                    const prefix = '[Augment Custom DEV]';
                    outputChannelLogger.debug(msg, ...args);
                    console.debug(`${prefix} ${msg}`, ...args);
                }
            };
            this.logger.info('Logger initialized in Development mode (outputting to Console and Output Channel).');

        } else {
            // 生产模式: 仅使用 OutputChannel logger
            this.logger = outputChannelLogger;
            this.logger.info('Logger initialized in Production mode.');
        }


        try {
            this.registerCommands();
            this.isInitialized = true;
            this.logger.info('Custom features initialized successfully');
        } catch (error) {
            this.logger.error('Failed to initialize custom features:', error);
            vscode.window.showErrorMessage('Augment 自定义功能初始化失败。');
            throw error;
        }
    }

    /**
     * 注册自定义命令
     */
    registerCommands() {
        const commands = [
            {
                id: 'augment.custom.newpool',
                handler: () => this.handleNewPool()
            }
        ];

        commands.forEach(cmd => {
            this.context.subscriptions.push(
                vscode.commands.registerCommand(cmd.id, cmd.handler)
            );
        });

        this.logger.info(`Registered ${commands.length} custom commands`);
    }

    // --- 数据处理核心方法 (无需改动) ---

    /**
     * @private
     * 安全地获取和解析 sessions 数据
     * @returns {Promise<object>} 返回解析后的 sessions 对象，失败则返回一个带默认值的对象
     */
    async _getAndParseSessions() {
        const defaultValue = { tenantURL: "https://d5.api.augmentcode.com/", scopes: ["email"], accessToken: null };
        try {
            const currentValue = await this.context.secrets.get(SESSIONS_KEY);
            return { ...defaultValue, ...(currentValue ? JSON.parse(currentValue) : {}) };
        } catch (error) {
            this.logger.error('Failed to parse sessions data from secrets, returning default value.', error);
            return defaultValue;
        }
    }

    /**
     * @private
     * 存储数据到 secrets
     * @param {object} data 要存储的数据
     * @returns {Promise<{success: boolean, data?: object, error?: string}>}
     */
    async _storeSessions(data) {
        try {
            await this.context.secrets.store(SESSIONS_KEY, JSON.stringify(data));
            this.logger.info(`Secret ${SESSIONS_KEY} stored successfully`);
            return { success: true, data };
        } catch (error) {
            this.logger.error(`Failed to store secret ${SESSIONS_KEY}:`, error);
            return { success: false, error: '存储会话数据失败' };
        }
    }

    /**
     * 获取 sessions 数据
     * @returns {Promise<{success: boolean, data?: object, error?: string}>}
     */
    async getSessionsData() {
        try {
            const data = await this._getAndParseSessions();
            if (data && data.accessToken) {
                return { success: true, data };
            } else {
                return { success: false, error: '未找到会话数据或 accessToken' };
            }
        } catch (error) {
            this.logger.error('Failed to get sessions data:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 更新 sessions 数据 (统一更新函数)
     * @param {object} updates - 一个包含要更新字段的对象，例如 { accessToken: 'new_token' }
     * @returns {Promise<{success: boolean, data?: object, error?: string}>}
     */
    async updateSessionsData(updates) {
        try {
            const currentData = await this._getAndParseSessions();
            const newData = { ...currentData, ...updates };
            return await this._storeSessions(newData);
        } catch (error) {
            this.logger.error('Failed to update sessions data:', error);
            return { success: false, error: error.message };
        }
    }


    // --- 命令处理器 (UI 逻辑) (无需改动) ---

    async handleNewPool() {
        try {
            const action = await vscode.window.showQuickPick([
                { label: '获取/查看会话信息', description: '查看当前的 accessToken 和 tenantURL' },
                { label: '设置/更新会话信息', description: '修改 accessToken 或 tenantURL' },
                { label: '更新机器码 (sessionId)', description: '重置设备唯一标识符' }
            ], { placeHolder: '选择要执行的操作' });

            if (!action) return;

            switch (action.label) {
                case '获取/查看会话信息':
                    await this.handleGetSession();
                    break;
                case '设置/更新会话信息':
                    await this.handleSetSession();
                    break;
                case '更新机器码 (sessionId)':
                    await this.handleUpdateMachineCode();
                    break;
            }
        } catch (error) {
            this.logger.error('Error in handleNewPool:', error);
            vscode.window.showErrorMessage(`操作失败: ${error.message}`);
        }
    }

    async handleGetSession() {
        const result = await this.getSessionsData();

        if (result.success) {
            const { accessToken, tenantURL } = result.data;
            const tokenDisplay = accessToken && accessToken.length > 16
                ? `${accessToken.substring(0, 8)}...${accessToken.substring(accessToken.length - 8)}`
                : accessToken || '未设置';

            const message = `Tenant URL: ${tenantURL}\nAccess Token: ${tokenDisplay}`;
            const action = await vscode.window.showInformationMessage(message, { modal: true }, '复制 AccessToken', '显示完整数据');

            if (action === '复制 AccessToken' && accessToken) {
                await vscode.env.clipboard.writeText(accessToken);
                vscode.window.showInformationMessage('AccessToken 已复制到剪贴板');
            } else if (action === '显示完整数据') {
                await this._showDataInNewDocument(result.data);
            }
        } else {
            vscode.window.showWarningMessage(`获取会话信息失败: ${result.error}`);
        }
    }
    
    async handleSetSession() {
        const action = await vscode.window.showQuickPick([
            { label: '仅更新 AccessToken', description: '快速更新当前会话的 AccessToken' },
            { label: '完整更新会话数据', description: '同时设置 Tenant URL 和 AccessToken' }
        ], { placeHolder: '选择更新方式' });

        if (!action) return;

        if (action.label === '仅更新 AccessToken') {
            await this._handleUpdateJustAccessToken();
        } else {
            await this._handleUpdateFullSession();
        }
    }
    
    async _handleUpdateJustAccessToken() {
        const newAccessToken = await vscode.window.showInputBox({
            prompt: '输入新的 AccessToken',
            placeHolder: '粘贴新的 AccessToken 到这里',
            password: true,
            ignoreFocusOut: true,
            validateInput: value => (!value || value.trim().length < 10) ? 'AccessToken 不能为空或太短' : null
        });

        if (!newAccessToken) return;

        const result = await this.updateSessionsData({ accessToken: newAccessToken.trim() });
        this._showUpdateResult(result);
    }
    
    async _handleUpdateFullSession() {
        const currentData = await this._getAndParseSessions();

        const newTenantURL = await vscode.window.showInputBox({
            prompt: '输入 Tenant URL',
            value: currentData.tenantURL,
            ignoreFocusOut: true,
            validateInput: value => {
                try {
                    new URL(value);
                    return null;
                } catch {
                    return '请输入有效的 URL';
                }
            }
        });
        if (!newTenantURL) return;

        const newAccessToken = await vscode.window.showInputBox({
            prompt: '输入新的 AccessToken',
            placeHolder: '粘贴新的 AccessToken 到这里',
            password: true,
            ignoreFocusOut: true,
            validateInput: value => (!value || value.trim().length < 10) ? 'AccessToken 不能为空或太短' : null
        });
        if (!newAccessToken) return;

        const result = await this.updateSessionsData({
            tenantURL: newTenantURL.trim(),
            accessToken: newAccessToken.trim()
        });
        this._showUpdateResult(result);
    }

    async handleUpdateMachineCode() {
        const newSessionId = crypto.randomUUID();
        await this.context.globalState.update(SESSION_ID_KEY, newSessionId);

        const selection = await vscode.window.showInformationMessage(
            `sessionId 更新成功！新值为: ${newSessionId}。需要重载窗口以生效。`,
            { modal: true },
            '立即重载'
        );
        if (selection === '立即重载') {
            vscode.commands.executeCommand('workbench.action.reloadWindow');
        }
    }
    
    // --- UI 辅助方法 (无需改动) ---
    
    /** @private */
    async _showUpdateResult(result) {
        if (result.success) {
            const selection = await vscode.window.showInformationMessage('会话数据更新成功！', '查看新数据');
            if (selection === '查看新数据') {
                await this._showDataInNewDocument(result.data);
            }
        } else {
            vscode.window.showErrorMessage(`更新失败: ${result.error}`);
        }
    }

    /** @private */
    async _showDataInNewDocument(data) {
        const content = JSON.stringify(data, null, 2);
        const doc = await vscode.workspace.openTextDocument({
            content,
            language: 'json'
        });
        await vscode.window.showTextDocument(doc, { preview: false });
    }

    /**
     * 清理资源
     */
    dispose() {
        // outputChannel 总是被创建，所以这里可以直接销毁
        if (this.outputChannel) {
            this.outputChannel.dispose();
        }
        this.isInitialized = false;
        // 在开发模式下，dispose 后依然可以在 console 中看到这条日志
        this.logger.info('Custom features disposed');
    }
}

module.exports = AugmentCustomFeatures;