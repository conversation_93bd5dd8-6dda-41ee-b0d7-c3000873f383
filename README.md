# my-scripts
基于 github action 的个人脚本库

1. 龙湖Token
    > App抓包
2. 京东cookie
    ```
    Safari浏览器登录 https://home.m.jd.com/myJd/newhome.action 点击个人中心页面后, 返回抓包app搜索关键字 info/GetJDUserInfoUnion 复制请求头Cookie字段填入json串数据内即可

    如需获取京东金融签到Body, 可进入"京东金融"APP (iOS), 在"首页"点击"签到"并签到一次, 返回抓包app搜索关键字 h5/m/appSign 复制请求体填入json串数据内即可
    ```
3. 52破解cookie
    ```
    cookie:登录网站获取cookie中的 htVD_2132_auth，htVD_2132_saltkey 就行了
    ```
4. 恩山cookie
    ```
    cookie:登录网站获取cookie中的 DJyZ_2132_auth，DJyZ_2132_saltkey 就行了
    ```
5. 集思录cookie
    ```
    https://www.jisilu.cn/data/cbnew/#cb
    找到接口 https://www.jisilu.cn/data/cbnew/cb_list_new/?___jsl=LST___t=1634804314662
    取cookie复制即可
    ```
6. 掘金cookie
    ```
    登录网站，点界面`去签到`按钮,过滤接口：check_in_rules，复制cookie即可，cookie一个月过期
    ```