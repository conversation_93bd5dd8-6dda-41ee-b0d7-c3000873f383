# 中国普法答题中奖查询系统

基于 Vue 3 + TypeScript + Tailwind CSS 开发的移动端中奖查询系统。

## 🚀 功能特性

- **活动列表展示** - 显示所有普法答题活动，包括进行中和已结束的活动
- **活动详情查看** - 查看特定活动的详细信息和中奖名单
- **智能搜索功能** - 支持按姓名或手机号搜索中奖信息
- **搜索结果展示** - 展示搜索到的中奖记录，支持跨活动搜索
- **无结果处理** - 友好的无结果页面，提供搜索建议和帮助
- **小红书推广** - 智能弹框推广，引导用户关注获取更多资讯
- **响应式设计** - 完美适配移动端，模拟 iPhone 界面风格

## 🛠️ 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全的 JavaScript 超集
- **Vite** - 快速的前端构建工具
- **Vue Router** - Vue.js 官方路由管理器
- **Pinia** - Vue 的状态管理库
- **Tailwind CSS** - 实用优先的 CSS 框架
- **Font Awesome** - 图标库

## 📦 安装和运行

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖

```bash
cd vue-legal-quiz
npm install
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:3000

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 📁 项目结构

```
vue-legal-quiz/
├── src/
│   ├── components/          # 公共组件
│   │   ├── LoadingSpinner.vue
│   │   └── PromotionModal.vue
│   ├── data/               # 模拟数据
│   │   └── mock.ts
│   ├── router/             # 路由配置
│   │   └── index.ts
│   ├── stores/             # 状态管理
│   │   └── activity.ts
│   ├── types/              # 类型定义
│   │   └── index.ts
│   ├── views/              # 页面组件
│   │   ├── ActivityList.vue
│   │   ├── ActivityDetail.vue
│   │   ├── SearchResults.vue
│   │   └── NoResults.vue
│   ├── App.vue             # 根组件
│   ├── main.ts             # 入口文件
│   └── style.css           # 全局样式
├── public/                 # 静态资源
├── index.html              # HTML 模板
├── package.json            # 项目配置
├── tailwind.config.js      # Tailwind 配置
├── tsconfig.json           # TypeScript 配置
└── vite.config.ts          # Vite 配置
```

## 🎯 核心功能说明

### 1. 活动管理
- 支持活动状态管理（进行中/已结束）
- 活动信息包含名称、描述、时间、中奖统计等
- 不同活动使用不同的主题色彩

### 2. 搜索功能
- 支持姓名和手机号搜索
- 支持模糊匹配和精确匹配
- 支持跨活动搜索和单活动搜索
- 实时搜索结果展示

### 3. 数据展示
- 中奖信息包含姓名、脱敏手机号、中奖时间
- 统一奖品：50元话费
- 状态标识：已发放/待发放

### 4. 用户体验
- iOS 风格的界面设计
- 流畅的页面切换动画
- 智能的搜索建议
- 友好的错误处理

## 🔧 自定义配置

### 修改主题色彩

在 `tailwind.config.js` 中修改：

```javascript
theme: {
  extend: {
    colors: {
      'legal-red': {
        500: '#dc2626',
        600: '#b91c1c',
      },
      'legal-gold': {
        400: '#fbbf24',
        500: '#f59e0b',
      }
    }
  }
}
```

### 修改模拟数据

在 `src/data/mock.ts` 中修改活动和中奖数据。

### 添加新页面

1. 在 `src/views/` 中创建新的 Vue 组件
2. 在 `src/router/index.ts` 中添加路由配置
3. 在相关页面添加导航链接

## 🚀 部署

### 静态部署

构建后将 `dist` 目录部署到任何静态文件服务器。

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

## 📝 开发说明

### 添加新的活动类型

1. 在 `src/types/index.ts` 中扩展 `Activity` 接口
2. 在 `src/data/mock.ts` 中添加新的模拟数据
3. 在相关组件中处理新的活动类型

### 集成真实 API

1. 创建 `src/api/` 目录
2. 实现 API 调用函数
3. 在 Pinia store 中替换模拟数据调用

### 自定义样式

项目使用 Tailwind CSS，可以：
- 在 `src/style.css` 中添加全局样式
- 在组件中使用 `<style scoped>` 添加组件样式
- 使用 Tailwind 的 `@layer` 指令添加自定义工具类

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
