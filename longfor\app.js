const axios = require("axios");
const sendMessage = require("../utils/sendmsg");
// 龙湖每日答题

let LFUserToken = process.env.LF_USERTOKEN || '7d6ec2b718834a2eae97a594c8fc0b53'
// 全局配置1
let config = {
    "X-LF-UserToken": LFUserToken,
    "X-LF-BU-Code": "C40501",
    "X-LF-Channel": "C4",
    "X-GAIA-API-key": "caed5282-9019-418d-8854-3c34d02e0b4e",
    "X-LF-DXRisk-Source": 1,
    "X-LF-DXRisk-Token": "6114800a9yObSxepaPuEG0wc7yzFnfvSRUEljFt1"
};
let apiHeader = {
    "channel": config["X-LF-Channel"],
    "token": config["X-LF-UserToken"],
    "bu_code": config["X-LF-BU-Code"],
};
axios.defaults.headers.common = config;

let apis = {
    "point": "https://longzhu.longfor.com/proxy/lmember-member-api-prod/api/member/v1/point",
    "balance": "https://longzhu.longfor.com/proxy/lmember-member-api-prod/api/member/v1/balance",
    "task": "https://longzhu.longfor.com/proxy/lmarketing-task-api-prod/openapi/task/v2/tasks",
    "anwser": "https://longzhu.longfor.com/proxy/lmarketing-task-api-prod/openapi/task/v1/information/user",
    "taskdetail": "https://longzhu.longfor.com/proxy/lmarketing-task-api-prod/openapi/task/v1/information/list?task_id=",
    "signClock": "https://longzhu.longfor.com/proxy/lmarketing-task-api-prod/openapi/task/v1/signs/clock",
};

async function run() {
    // 签到

    let signRet = await axios.post(apis.signClock, {
        "task_id": 28,
        ...apiHeader,
    });
    let signResult = `签到: ${signRet.data.message}`;
    console.log(signResult);
    await sendMessage(`龙湖任务: \n${signResult}`);
    /* let answerResultSum = ''
    // 获取当前登录用户信息
    let ret = await axios.get(apis.task);
    if (ret.data.message === "OK") {
        if (ret.data.data.tasks.length > 0) {
            console.log(`发现了${ret.data.data.tasks.length}个任务`);
            let {
                task_id
            } = (ret.data.data.tasks.filter((item) => item.type === 'information'))[0]
            let taskdetailRet = await axios.get(apis.taskdetail + task_id);
            if (taskdetailRet.data.message === "OK") {
                let {
                    item_id
                } = taskdetailRet.data.data.information[0];
                let {
                    item_content
                } = taskdetailRet.data.data.information[0];
                console.log(`item_id=${item_id},item_content=${item_content}`);
                let item_content_anwser = {
                    "user_answer": [1, 0, 0, 0]
                };
                let anwserRet = await axios.post(apis.anwser, {
                    task_id,
                    item_id,
                    "item_content": JSON.stringify(item_content_anwser),
                    ...apiHeader,
                });
                let answerResult = `小程序答题: ${anwserRet.data.message}`;
                console.log(answerResult);
                answerResultSum = answerResultSum + answerResult;
            }

            // 公众号答题
            let gzhFlag = false;
            let gzhTaskId = task_id + 1
            let gzhTaskdetailRet = await axios.get(apis.taskdetail + gzhTaskId);
            if (gzhTaskdetailRet.data.message === "OK") {
                gzhFlag = true
            } else {
                // 尝试任务ID-1
                gzhTaskId = task_id - 1
                gzhTaskdetailRet = await axios.get(apis.taskdetail + gzhTaskId);
                if (gzhTaskdetailRet.data.message === "OK") {
                    gzhFlag = true
                } else {
                    gzhFlag = false
                }
            }

            if (gzhFlag) {
                let {
                    item_id
                } = gzhTaskdetailRet.data.data.information[0];
                let {
                    item_content
                } = gzhTaskdetailRet.data.data.information[0];
                console.log(`item_id=${item_id},item_content=${item_content}`);
                let item_content_anwser = {
                    "user_answer": [1, 0, 0, 0]
                };
                let anwserRet = await axios.post(apis.anwser, {
                    task_id: gzhTaskId,
                    item_id,
                    "item_content": JSON.stringify(item_content_anwser),
                    ...apiHeader,
                });
                let answerResult = `\n公众号答题: ${anwserRet.data.message}`;
                console.log(answerResult);
                answerResultSum = answerResultSum + answerResult;
            }
            
            let balanceRet = await axios.post(apis.balance, {
                ...apiHeader
            }, {
                "headers": {
                    "X-GAIA-API-KEY": "5e58b944-a3cd-4fb9-b09a-a7529371de1a"
                }
            });

            let balanceResult = `珑珠: ${balanceRet.data.data.balance}`;
            console.log(balanceResult);
            await sendMessage(`龙湖任务: \n${signResult}\n${answerResultSum}\n${balanceResult}`);

        }
    } else {
        await sendMessage(`龙湖任务: \n${signResult}`);
    } */

}

run();