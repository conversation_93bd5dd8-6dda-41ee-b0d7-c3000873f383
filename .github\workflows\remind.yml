name: <PERSON><PERSON>ub remind bot
on:
  # push: # 推送触发
  workflow_dispatch: # 手动触发
  schedule:
    - cron: '33 1 * * *'
jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout codes
        uses: actions/checkout@v2
        with:
          persist-credentials: false
      - name: Use Node.js 12.x
        uses: actions/setup-node@v1
        with:
          node-version: '12.x'
      - name: Run app
        run: npm install
      - run: npm run remind
        env:
          JSL_COOKIE: ${{ secrets.JSL_COOKIE }}