# 功能特性说明

## 🎯 核心功能

### 1. 中奖结果公布状态管理

#### 状态类型
- **`published`** - 已公布中奖结果
- **`unpublished`** - 未公布中奖结果

#### 业务逻辑
- 🔴 **进行中的活动** → 自动设置为 `unpublished`
- 🟡 **已结束的活动** → 可以是 `published` 或 `unpublished`
- 🟢 **只有 `published` 状态的活动才能查询中奖名单**

### 2. 智能搜索系统

#### 首页快速过滤
```typescript
// 实时过滤活动列表
const filteredActivities = computed(() => {
  if (!searchKeyword.value.trim()) {
    return activityStore.activities
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return activityStore.activities.filter(activity => 
    activity.name.toLowerCase().includes(keyword) ||
    activity.description.toLowerCase().includes(keyword)
  )
})
```

#### 中奖名单搜索
- ✅ 只在已公布结果的活动中搜索
- ✅ 支持姓名和手机号搜索
- ✅ 跨活动搜索功能
- ❌ 未公布结果的活动无法搜索

### 3. 状态感知界面

#### 活动卡片显示
```typescript
// 根据公布状态显示不同内容
{
  winnerCount: activity.resultStatus === 'published' ? activity.winnerCount : '待公布',
  totalPrize: activity.resultStatus === 'published' ? `¥${activity.totalPrize}` : '待公布',
  status: getResultStatusText(activity.resultStatus)
}
```

#### 颜色编码
- 🟢 **绿色** - 已公布结果
- 🟡 **橙色** - 未公布结果
- 🔵 **蓝色** - 进行中活动

## 📊 数据结构

### Activity 接口
```typescript
interface Activity {
  id: string
  name: string
  description: string
  icon: string
  status: 'active' | 'ended'
  resultStatus: 'published' | 'unpublished' // 新增字段
  winnerCount: number // 未公布时为 0
  totalPrize: number // 未公布时为 0
  startDate: string
  endDate: string
  color: string
}
```

### 示例数据
```typescript
// 进行中 + 未公布 = 不可查询
{
  id: '1',
  name: '2024年宪法宣传周答题活动',
  status: 'active',
  resultStatus: 'unpublished',
  winnerCount: 0,
  totalPrize: 0
}

// 已结束 + 已公布 = 可查询
{
  id: '2', 
  name: '民法典知识竞赛',
  status: 'ended',
  resultStatus: 'published',
  winnerCount: 89,
  totalPrize: 4450
}
```

## 🔍 搜索逻辑

### 1. 首页搜索（快速过滤）
- **触发方式**: 输入框实时输入
- **搜索范围**: 所有活动
- **搜索字段**: 活动名称、描述
- **结果展示**: 实时过滤下方活动列表

### 2. 详情页搜索（中奖名单）
- **触发方式**: 回车键或搜索按钮
- **搜索范围**: 当前活动的中奖名单
- **前置条件**: 活动结果状态为 `published`
- **搜索字段**: 中奖用户姓名、手机号

### 3. 全局搜索（跨活动）
- **触发方式**: 从首页或详情页跳转
- **搜索范围**: 所有已公布结果的活动
- **搜索字段**: 中奖用户姓名、手机号
- **结果展示**: 专门的搜索结果页面

## 🎨 界面状态

### 活动列表页面
```vue
<!-- 根据结果公布状态显示 -->
<div class="stat-item">
  <div class="text-xl font-bold text-red-600">
    {{ activity.resultStatus === 'published' ? activity.winnerCount : '待公布' }}
  </div>
  <div class="text-xs text-gray-600">中奖人数</div>
</div>
```

### 活动详情页面
```vue
<!-- 未公布结果的提示 -->
<div v-if="activity.resultStatus === 'unpublished'" class="text-center py-16 text-white">
  <div class="empty-icon mx-auto mb-6">
    <i class="fas fa-clock text-white text-4xl opacity-60"></i>
  </div>
  <h3 class="text-xl font-bold mb-4">中奖结果尚未公布</h3>
  <p class="text-sm opacity-80 mb-2">
    {{ activity.status === 'active' ? '活动正在进行中' : '活动已结束' }}
  </p>
</div>
```

## 🔧 Store 方法

### 计算属性
```typescript
// 已公布结果的活动（可查询中奖名单）
const publishedActivities = computed(() => 
  activities.value.filter(activity => activity.resultStatus === 'published')
)

// 未公布结果的活动
const unpublishedActivities = computed(() => 
  activities.value.filter(activity => activity.resultStatus === 'unpublished')
)
```

### 搜索方法
```typescript
const searchWinners = async (params: SearchParams): Promise<SearchResult> => {
  // 只在已公布结果的活动中搜索
  const publishedActivityIds = publishedActivities.value.map(activity => activity.id)
  let filteredWinners = mockWinners.filter(winner => 
    publishedActivityIds.includes(winner.activityId)
  )
  
  // 继续其他搜索逻辑...
}
```

## 🚀 使用场景

### 场景1: 活动进行中
- **状态**: `active` + `unpublished`
- **显示**: "待公布"
- **功能**: 无法查询中奖名单
- **提示**: "活动正在进行中，请等待活动结束后查看中奖结果"

### 场景2: 活动结束但未公布结果
- **状态**: `ended` + `unpublished`  
- **显示**: "待公布"
- **功能**: 无法查询中奖名单
- **提示**: "活动已结束，中奖结果将在近期公布，请耐心等待"

### 场景3: 活动结束且已公布结果
- **状态**: `ended` + `published`
- **显示**: 实际中奖数据
- **功能**: 可以查询中奖名单
- **操作**: 支持搜索、查看详情等所有功能

## 📱 用户体验

### 视觉反馈
- 🟢 已公布：绿色文字 + 勾选图标
- 🟡 未公布：橙色文字 + 时钟图标
- 📊 数据显示：实际数字 vs "待公布"文字

### 交互反馈
- 🔍 搜索框：已公布活动显示，未公布活动隐藏
- 🚫 功能限制：未公布活动显示友好提示
- ⚡ 实时过滤：首页搜索即时响应

### 信息架构
- 📋 活动列表：状态一目了然
- 📄 活动详情：状态感知的功能可用性
- 🔎 搜索结果：只显示有效数据
