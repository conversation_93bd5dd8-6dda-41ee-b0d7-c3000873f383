<template>
  <div 
    ref="modalRef"
    class="modal-overlay"
    :class="{ show: isVisible }"
    @click="handleOverlayClick"
  >
    <div class="modal-content">
      <div class="xiaohongshu-icon">
        <i class="fab fa-instagram text-white text-2xl"></i>
      </div>
      <h3 class="text-lg font-bold text-gray-800 mb-2">关注小红书获取更多</h3>
      <p class="text-sm text-gray-600 mb-4">最新普法资讯 · 答题答案</p>
      <div class="flex justify-center">
        <button class="modal-btn" @click="followXiaohongshu">
          <i class="fab fa-instagram mr-2"></i>
          立即关注
        </button>
        <button class="modal-btn secondary" @click="closeModal">
          稍后再说
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const modalRef = ref<HTMLElement>()
const isVisible = ref(false)

// 检查用户是否已经关注
const isFollowed = ref(false)

// 从 localStorage 获取关注状态
const getFollowStatus = (): boolean => {
  try {
    const status = localStorage.getItem('xiaohongshu_followed')
    return status === 'true'
  } catch (error) {
    console.error('获取关注状态失败:', error)
    return false
  }
}

// 保存关注状态到 localStorage
const saveFollowStatus = (status: boolean) => {
  try {
    localStorage.setItem('xiaohongshu_followed', status.toString())
  } catch (error) {
    console.error('保存关注状态失败:', error)
  }
}

// 显示弹框
const showModal = () => {
  // 如果用户已经关注，则不显示弹框
  if (isFollowed.value) {
    console.log('用户已关注，不显示弹框')
    return
  }
  isVisible.value = true
}

// 关闭弹框
const closeModal = () => {
  isVisible.value = false
}

// 关注小红书
const followXiaohongshu = () => {
  console.log('跳转到小红书关注页面')
  window.open('https://www.xiaohongshu.com/user/profile/6086ba01000000000100b9a7')
  // 标记用户已关注
  isFollowed.value = true
  saveFollowStatus(true)

  closeModal()
}

// 点击遮罩层关闭弹框
const handleOverlayClick = (event: MouseEvent) => {
  if (event.target === modalRef.value) {
    closeModal()
  }
}

// 根据路由决定显示时机
const getDelayByRoute = (routeName: string | undefined): number => {
  switch (routeName) {
    case 'Home':
      return 1000 // 活动列表页面 1 秒后显示
    case 'ActivityDetail':
      return 1500 // 活动详情页面 1.5 秒后显示
    case 'Search':
      return 2000 // 搜索结果页面 2 秒后显示
    case 'NoResults':
      return 1000 // 无结果页面 1 秒后显示（更快显示）
    default:
      return 1500
  }
}

onMounted(() => {
  // 初始化关注状态
  isFollowed.value = getFollowStatus()

  // 根据当前路由设置不同的延迟时间
  const delay = getDelayByRoute(router.currentRoute.value.name as string)

  setTimeout(() => {
    showModal()
  }, delay)
})

// 监听路由变化，在新页面也显示弹框
router.afterEach((to) => {
  if (isVisible.value) {
    closeModal()
  }

  // 只有在用户未关注的情况下才显示弹框
  if (!isFollowed.value) {
    const delay = getDelayByRoute(to.name as string)
    setTimeout(() => {
      showModal()
    }, delay)
  }
})
</script>
