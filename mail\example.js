/**
 * 邮件发送示例脚本
 * 演示如何使用 EmailSender 类发送自定义邮件
 */

const EmailSender = require('./mail.js');

/**
 * 自定义邮件发送器
 */
class CustomEmailSender extends EmailSender {
    /**
     * 生成自定义邮件内容
     */
    generateCustomEmailContent(options = {}) {
        const {
            subject = '自定义邮件主题',
            title = '自定义邮件标题',
            content = '这是自定义的邮件内容',
            data = {}
        } = options;

        const now = new Date();
        const currentDate = now.toLocaleString('zh-CN');

        const htmlContent = `
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${title}</title>
            <style>
                body {
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f5f5f5;
                }
                .container {
                    background-color: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .header {
                    text-align: center;
                    border-bottom: 2px solid #2196F3;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }
                .header h1 {
                    color: #2196F3;
                    margin: 0;
                }
                .content {
                    margin-bottom: 25px;
                }
                .data-section {
                    background-color: #f8f9fa;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 15px 0;
                }
                .footer {
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #eee;
                    color: #888;
                    font-size: 14px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>${title}</h1>
                    <p>发送时间: ${currentDate}</p>
                </div>
                
                <div class="content">
                    <p>${content}</p>
                </div>

                ${Object.keys(data).length > 0 ? `
                <div class="data-section">
                    <h3>📊 数据信息</h3>
                    ${Object.entries(data).map(([key, value]) => 
                        `<p><strong>${key}:</strong> ${value}</p>`
                    ).join('')}
                </div>
                ` : ''}

                <div class="footer">
                    <p>此邮件由 Node.js 自动发送</p>
                    <p>发送时间: ${currentDate}</p>
                </div>
            </div>
        </body>
        </html>
        `;

        const textContent = `
${title}

${content}

${Object.keys(data).length > 0 ? 
    '数据信息:\n' + Object.entries(data).map(([key, value]) => `- ${key}: ${value}`).join('\n') + '\n'
    : ''
}

---
此邮件由 Node.js 自动发送
发送时间: ${currentDate}
        `;

        return {
            subject,
            html: htmlContent,
            text: textContent
        };
    }

    /**
     * 发送自定义邮件
     */
    async sendCustomEmail(options) {
        try {
            // 检查配置
            if (!this.config.sender.password) {
                throw new Error('请设置发送者邮箱授权码 (SENDER_PASSWORD 环境变量)');
            }

            // 初始化传输器
            const isConnected = await this.initTransporter();
            if (!isConnected) {
                throw new Error('无法连接到SMTP服务器');
            }

            // 生成邮件内容
            const emailContent = this.generateCustomEmailContent(options);

            // 邮件选项
            const mailOptions = {
                from: {
                    name: this.config.sender.name,
                    address: this.config.sender.email
                },
                to: {
                    name: this.config.recipient.name,
                    address: this.config.recipient.email
                },
                subject: emailContent.subject,
                text: emailContent.text,
                html: emailContent.html
            };

            console.log('📧 正在发送自定义邮件...');
            console.log(`主题: ${emailContent.subject}`);

            // 发送邮件
            const info = await this.transporter.sendMail(mailOptions);

            console.log('✅ 自定义邮件发送成功!');
            console.log(`消息ID: ${info.messageId}`);

            return {
                success: true,
                messageId: info.messageId,
                info: info
            };

        } catch (error) {
            console.error('❌ 自定义邮件发送失败:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

/**
 * 示例用法
 */
async function examples() {
    console.log('📧 邮件发送示例演示');
    console.log('='.repeat(50));

    const customSender = new CustomEmailSender();

    try {
        // 示例1: 发送简单自定义邮件
        console.log('\n📝 示例1: 发送简单自定义邮件');
        const result1 = await customSender.sendCustomEmail({
            subject: '测试邮件 - 简单示例',
            title: '🎉 测试邮件',
            content: '这是一封测试邮件，用于验证自定义邮件发送功能。'
        });

        if (result1.success) {
            console.log('✅ 示例1 发送成功');
        }

        // 示例2: 发送包含数据的邮件
        console.log('\n📊 示例2: 发送包含数据的邮件');
        const result2 = await customSender.sendCustomEmail({
            subject: '数据报告 - ' + new Date().toLocaleDateString(),
            title: '📊 每日数据报告',
            content: '以下是今日的数据统计报告：',
            data: {
                '用户数量': '1,234',
                '订单数量': '567',
                '收入': '¥12,345',
                '增长率': '+15.6%'
            }
        });

        if (result2.success) {
            console.log('✅ 示例2 发送成功');
        }

    } catch (error) {
        console.error('❌ 示例执行出错:', error.message);
    } finally {
        customSender.close();
    }
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
    examples();
}

module.exports = CustomEmailSender;
