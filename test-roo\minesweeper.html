<!DOCTYPE html>
<html>
<head>
    <title>扫雷游戏</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 20px;
        }
        .game-container {
            margin-top: 20px;
        }
        .board {
            display: grid;
            grid-template-columns: repeat(10, 30px);
            grid-gap: 2px;
        }
        .cell {
            width: 30px;
            height: 30px;
            background-color: #ddd;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-weight: bold;
            user-select: none;
        }
        .cell.revealed {
            background-color: #fff;
        }
        .cell.mine {
            background-color: red;
        }
        .cell.flagged {
            background-color: #ffeb3b;
        }
        .controls {
            margin-bottom: 20px;
        }
        button {
            padding: 8px 16px;
            cursor: pointer;
        }
        .instructions {
            margin-top: 20px;
            max-width: 500px;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>扫雷游戏</h1>
    <div class="controls">
        <button id="restart">重新开始</button>
    </div>
    <div class="game-container">
        <div class="board" id="board"></div>
    </div>
    <div class="instructions">
        <p>左键点击：揭开格子</p>
        <p>右键点击：标记/取消标记地雷</p>
    </div>

    <script>
        const BOARD_SIZE = 10;
        const MINE_COUNT = 15;
        let board = [];
        let gameOver = false;

        function initBoard() {
            gameOver = false;
            const boardElement = document.getElementById('board');
            boardElement.innerHTML = '';
            board = Array(BOARD_SIZE).fill().map(() => Array(BOARD_SIZE).fill(0));

            // 放置地雷
            let minesPlaced = 0;
            while (minesPlaced < MINE_COUNT) {
                const x = Math.floor(Math.random() * BOARD_SIZE);
                const y = Math.floor(Math.random() * BOARD_SIZE);
                if (board[y][x] !== -1) {
                    board[y][x] = -1;
                    minesPlaced++;
                }
            }

            // 计算每个格子周围的地雷数
            for (let y = 0; y < BOARD_SIZE; y++) {
                for (let x = 0; x < BOARD_SIZE; x++) {
                    if (board[y][x] === -1) continue;
                    
                    let count = 0;
                    for (let dy = -1; dy <= 1; dy++) {
                        for (let dx = -1; dx <= 1; dx++) {
                            const ny = y + dy;
                            const nx = x + dx;
                            if (ny >= 0 && ny < BOARD_SIZE && nx >= 0 && nx < BOARD_SIZE && board[ny][nx] === -1) {
                                count++;
                            }
                        }
                    }
                    board[y][x] = count;
                }
            }

            // 创建格子元素
            for (let y = 0; y < BOARD_SIZE; y++) {
                for (let x = 0; x < BOARD_SIZE; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.dataset.x = x;
                    cell.dataset.y = y;
                    
                    // 左键点击事件
                    cell.addEventListener('click', () => handleCellClick(x, y));
                    
                    // 右键点击事件
                    cell.addEventListener('contextmenu', (e) => {
                        e.preventDefault();
                        handleRightClick(x, y);
                    });
                    
                    boardElement.appendChild(cell);
                }
            }
        }

        function handleCellClick(x, y) {
            if (gameOver) return;
            
            const cell = document.querySelector(`.cell[data-x="${x}"][data-y="${y}"]`);
            if (cell.classList.contains('revealed') || cell.classList.contains('flagged')) return;

            cell.classList.add('revealed');
            
            if (board[y][x] === -1) {
                cell.textContent = '💣';
                cell.classList.add('mine');
                revealAllMines();
                gameOver = true;
                alert('游戏结束！你踩到地雷了！');
                return;
            }

            if (board[y][x] > 0) {
                cell.textContent = board[y][x];
            } else {
                // 如果是空白格子，自动展开周围的空白格子
                revealEmptyCells(x, y);
            }

            checkWin();
        }

        function handleRightClick(x, y) {
            if (gameOver) return;
            
            const cell = document.querySelector(`.cell[data-x="${x}"][data-y="${y}"]`);
            if (cell.classList.contains('revealed')) return;
            
            if (cell.classList.contains('flagged')) {
                cell.classList.remove('flagged');
                cell.textContent = '';
            } else {
                cell.classList.add('flagged');
                cell.textContent = '🚩';
            }
        }

        function revealEmptyCells(x, y) {
            for (let dy = -1; dy <= 1; dy++) {
                for (let dx = -1; dx <= 1; dx++) {
                    const ny = y + dy;
                    const nx = x + dx;
                    if (ny >= 0 && ny < BOARD_SIZE && nx >= 0 && nx < BOARD_SIZE) {
                        const cell = document.querySelector(`.cell[data-x="${nx}"][data-y="${ny}"]`);
                        if (!cell.classList.contains('revealed') && !cell.classList.contains('flagged')) {
                            cell.classList.add('revealed');
                            if (board[ny][nx] > 0) {
                                cell.textContent = board[ny][nx];
                            } else if (board[ny][nx] === 0) {
                                revealEmptyCells(nx, ny);
                            }
                        }
                    }
                }
            }
        }

        function revealAllMines() {
            for (let y = 0; y < BOARD_SIZE; y++) {
                for (let x = 0; x < BOARD_SIZE; x++) {
                    if (board[y][x] === -1) {
                        const cell = document.querySelector(`.cell[data-x="${x}"][data-y="${y}"]`);
                        cell.textContent = '💣';
                        cell.classList.add('revealed', 'mine');
                    }
                }
            }
        }

        function checkWin() {
            let unrevealedSafeCells = 0;
            for (let y = 0; y < BOARD_SIZE; y++) {
                for (let x = 0; x < BOARD_SIZE; x++) {
                    const cell = document.querySelector(`.cell[data-x="${x}"][data-y="${y}"]`);
                    if (!cell.classList.contains('revealed') && board[y][x] !== -1) {
                        unrevealedSafeCells++;
                    }
                }
            }
            
            if (unrevealedSafeCells === 0) {
                gameOver = true;
                alert('恭喜你赢了！');
            }
        }

        document.getElementById('restart').addEventListener('click', initBoard);

        // 初始化游戏
        initBoard();
    </script>
</body>
</html>