<template>
  <div class="min-h-screen">
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="flex items-center justify-between p-4">
        <i class="fas fa-arrow-left text-white text-lg cursor-pointer" @click="goBack"></i>
        <h1 class="text-white text-lg font-semibold">查询结果</h1>
        <i class="fas fa-redo text-white text-lg cursor-pointer" @click="clearAndRetry"></i>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <i class="fas fa-search text-gray-400 mr-3"></i>
      <input 
        v-model="searchKeyword"
        type="text" 
        class="search-input" 
        :placeholder="currentKeyword"
        @keypress.enter="handleSearch"
      >
      <i 
        class="fas fa-times text-gray-400 ml-3 cursor-pointer" 
        @click="clearAndRetry"
      ></i>
    </div>

    <!-- 空状态 -->
    <div class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-search text-white text-4xl opacity-60"></i>
      </div>
      
      <h2 class="text-2xl font-bold mb-4">未找到相关信息</h2>
      <p class="text-lg opacity-80 mb-2">很抱歉，没有找到"{{ currentKeyword }}"的中奖记录</p>
      <p class="text-sm opacity-60 leading-relaxed">
        请检查输入的姓名或手机号是否正确<br>
        或尝试在不同活动中搜索
      </p>
      
      <button class="retry-btn" @click="clearAndRetry">
        <i class="fas fa-redo mr-2"></i>
        重新搜索
      </button>
    </div>

    <!-- 搜索建议 -->
    <div class="suggestion-card">
      <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
        搜索建议
      </h3>
      
      <div class="space-y-3">
        <div 
          v-for="suggestion in searchSuggestions" 
          :key="suggestion.text"
          class="suggestion-item"
          @click="searchSuggestion(suggestion.text)"
        >
          <i :class="suggestion.icon" class="text-red-500 mr-3"></i>
          <div class="flex-1">
            <p class="font-medium text-gray-800">尝试搜索：{{ suggestion.text }}</p>
            <p class="text-sm text-gray-600">{{ suggestion.description }}</p>
          </div>
          <div class="activity-suggestion">{{ suggestion.badge }}</div>
        </div>
      </div>
    </div>

    <!-- 活动推荐 -->
    <div class="suggestion-card">
      <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
        <i class="fas fa-fire text-red-500 mr-2"></i>
        热门活动
      </h3>
      
      <div class="space-y-3">
        <div 
          v-for="activity in hotActivities" 
          :key="activity.id"
          class="suggestion-item"
          @click="viewActivity(activity.id)"
        >
          <i :class="activity.icon" class="text-green-500 mr-3"></i>
          <div class="flex-1">
            <p class="font-medium text-gray-800">{{ activity.name }}</p>
            <p class="text-sm text-gray-600">{{ activity.winnerCount }}人中奖 · {{ activity.status === 'active' ? '进行中' : '已结束' }}</p>
          </div>
          <i class="fas fa-chevron-right text-gray-400"></i>
        </div>
      </div>
    </div>

    <!-- 帮助信息 -->
    <div class="suggestion-card">
      <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
        <i class="fas fa-question-circle text-blue-500 mr-2"></i>
        搜索帮助
      </h3>
      
      <div class="space-y-3 text-sm text-gray-600">
        <div 
          v-for="help in helpItems" 
          :key="help.title"
          class="flex items-start"
        >
          <i class="fas fa-check text-green-500 mr-3 mt-0.5"></i>
          <div>
            <p class="font-medium text-gray-800">{{ help.title }}</p>
            <p>{{ help.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useActivityStore } from '@/stores/activity'

const route = useRoute()
const router = useRouter()
const activityStore = useActivityStore()

const searchKeyword = ref('')

// 当前搜索关键词
const currentKeyword = computed(() => {
  return route.query.keyword as string || ''
})

// 热门活动（取前两个已公布结果的活动）
const hotActivities = computed(() => {
  return activityStore.publishedActivities.slice(0, 2)
})

// 搜索建议
const searchSuggestions = ref([
  {
    text: '张三',
    description: '常见中奖用户',
    icon: 'fas fa-user',
    badge: '3次中奖'
  },
  {
    text: '138****5678',
    description: '手机号格式示例',
    icon: 'fas fa-phone',
    badge: '已中奖'
  },
  {
    text: '王',
    description: '使用部分姓名搜索',
    icon: 'fas fa-search',
    badge: '模糊匹配'
  }
])

// 帮助信息
const helpItems = ref([
  {
    title: '支持姓名搜索',
    description: '输入完整姓名或部分姓名进行查询'
  },
  {
    title: '支持手机号搜索',
    description: '输入完整手机号或带*号的脱敏手机号'
  },
  {
    title: '跨活动搜索',
    description: '可以搜索用户在所有活动中的中奖记录'
  },
  {
    title: '实时更新',
    description: '中奖信息实时同步，确保数据准确'
  }
])

// 返回上一页
const goBack = () => {
  router.back()
}

// 清除并重试
const clearAndRetry = () => {
  searchKeyword.value = ''
  // 跳转回首页
  router.push('/')
}

// 处理搜索
const handleSearch = () => {
  const keyword = searchKeyword.value.trim()
  if (keyword) {
    router.push({
      name: 'Search',
      query: { keyword }
    })
  }
}

// 搜索建议
const searchSuggestion = (text: string) => {
  router.push({
    name: 'Search',
    query: { keyword: text }
  })
}

// 查看活动
const viewActivity = (activityId: string) => {
  router.push(`/activity/${activityId}`)
}
</script>

<style scoped>
.empty-state {
  @apply text-center py-16 px-10 text-white;
}

.empty-icon {
  @apply w-32 h-32 bg-white bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-6 backdrop-blur-lg border-2 border-white border-opacity-20;
}

.retry-btn {
  @apply bg-white bg-opacity-20 backdrop-blur-lg text-white border-2 border-white border-opacity-30 px-6 py-3 rounded-full text-base font-semibold mt-6 transition-all hover:bg-opacity-30 hover:-translate-y-1;
}

.suggestion-card {
  @apply bg-white bg-opacity-90 backdrop-blur-lg mx-5 my-5 p-5 rounded-2xl border border-white border-opacity-20;
}

.suggestion-item {
  @apply flex items-center p-3 bg-gray-50 rounded-lg cursor-pointer transition-all hover:bg-gray-100 hover:translate-x-1;
}

.activity-suggestion {
  @apply bg-gradient-to-r from-legal-red-500 to-legal-red-600 text-white px-2 py-1 rounded-xl text-xs font-semibold ml-2;
}
</style>
