<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国普法答题中奖查询系统 - 原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .phone-container {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 8px;
            box-shadow: 0 0 30px rgba(0,0,0,0.3);
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 39px;
            overflow: hidden;
            position: relative;
        }
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
            padding: 40px;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #fbbf24 100%);
            min-height: 100vh;
        }
        .prototype-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .prototype-title {
            font-size: 18px;
            font-weight: 600;
            color: white;
            margin-bottom: 16px;
            text-align: center;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        iframe {
            border: none;
            width: 100%;
            height: 100%;
        }
        .header-section {
            text-align: center;
            margin-bottom: 48px;
            color: white;
        }
        .header-icon {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
        }
    </style>
</head>
<body>
    <div class="container mx-auto py-8">
        <div class="header-section">
            <div class="header-icon">
                <i class="fas fa-balance-scale text-white text-3xl"></i>
            </div>
            <h1 class="text-4xl font-bold mb-4">中国普法答题中奖查询系统</h1>
            <p class="text-xl opacity-90">高保真移动端原型展示</p>
            <p class="text-sm opacity-75 mt-2">弘扬法治精神 · 建设法治中国</p>
        </div>

        <div class="prototype-grid">
            <!-- 活动列表页面 -->
            <div class="prototype-item">
                <h2 class="prototype-title">活动列表页面</h2>
                <div class="phone-container">
                    <div class="phone-screen">
                        <iframe src="activity-list.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 活动详情页面 -->
            <div class="prototype-item">
                <h2 class="prototype-title">活动详情页面</h2>
                <div class="phone-container">
                    <div class="phone-screen">
                        <iframe src="activity-detail.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 搜索结果页面 -->
            <div class="prototype-item">
                <h2 class="prototype-title">搜索结果页面</h2>
                <div class="phone-container">
                    <div class="phone-screen">
                        <iframe src="search-results.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 无结果页面 -->
            <div class="prototype-item">
                <h2 class="prototype-title">无结果页面</h2>
                <div class="phone-container">
                    <div class="phone-screen">
                        <iframe src="no-results.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 弹框展示页面 -->
            <div class="prototype-item">
                <h2 class="prototype-title">小红书推广弹框</h2>
                <div class="phone-container">
                    <div class="phone-screen">
                        <iframe src="modal-demo.html"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>