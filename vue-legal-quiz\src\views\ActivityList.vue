<template>
  <div class="min-h-screen">
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="flex items-center justify-between p-4">
        <div class="w-6"></div>
        <h1 class="text-white text-lg font-semibold flex items-center">
          <i class="fas fa-balance-scale mr-2"></i>
          中国普法答题活动
        </h1>
        <div class="flex items-center space-x-4">
          <!-- 分享按钮 -->
          <i
            class="fas fa-share-alt text-white text-lg cursor-pointer hover:text-yellow-300 transition-colors duration-200"
            @click="shareCurrentPage"
          ></i>

          <!-- 通知按钮 -->
          <div class="relative">
            <i
              class="fas fa-bell text-white text-lg cursor-pointer hover:text-yellow-300 transition-colors duration-200"
              @click="showNotifications"
            ></i>
            <!-- 未读通知小红点 -->
            <div
              v-if="hasUnreadNotifications"
              class="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <i class="fas fa-search text-gray-400 mr-3"></i>
      <input
        v-model="searchKeyword"
        type="text"
        class="search-input"
        placeholder="快速过滤活动名称或关键词"
        @input="filterActivities"
      >
      <i class="fas fa-times text-gray-400 ml-3 cursor-pointer" @click="clearSearch" v-if="searchKeyword"></i>
    </div>

    <!-- 活动列表 -->
    <div class="flex-1 pb-6">
      <LoadingSpinner v-if="activityStore.loading" />
      
      <template v-else>
        <!-- 活动卡片 -->
        <div
          v-for="activity in filteredActivities"
          :key="activity.id"
          class="activity-card cursor-pointer"
          @click="goToActivity(activity.id)"
        >
          <div class="flex items-start justify-between mb-4">
            <div class="flex items-center">
              <div class="activity-icon mr-4">
                <i :class="activity.icon" class="text-white text-xl"></i>
              </div>
              <div>
                <h3 class="text-lg font-bold text-gray-800">{{ activity.name.substr(0, 7) }}</h3>
                <p class="text-gray-600 text-sm">{{ activity.description.substr(0, 10) }}</p>
              </div>
            </div>
            <div class="status-badge" :class="getStatusClass(activity.status)">
              <i :class="getStatusIcon(activity.status)" class="mr-1"></i>
              {{ getStatusText(activity.status) }}
            </div>
          </div>
          
          <div class="stats-grid">
            <div class="stat-item">
              <div class="text-xl font-bold text-red-600">
                {{ activity.winnerCount || '待公布' }}
              </div>
              <div class="text-xs text-gray-600">中奖人数</div>
            </div>
            <div class="stat-item">
              <div class="text-xl font-bold text-yellow-600">
                {{  formatAmount(activity.totalPrize) || '待公布' }}
              </div>
              <div class="text-xs text-gray-600">总奖金</div>
            </div>
            <div class="stat-item">
              <div class="text-xl font-bold" :class="getResultStatusColor(activity.resultStatus)">
                {{ getResultStatusText(activity.resultStatus) }}
              </div>
              <div class="text-xs text-gray-600">中奖结果</div>
            </div>
          </div>
          
          <div class="mt-4 pt-4 border-t border-gray-200">
            <p class="text-xs text-gray-500 flex items-center">
              <i class="fas fa-calendar mr-2"></i>
              活动时间：{{ activity.startDate }} 至 {{ activity.endDate }}
            </p>
          </div>
        </div>
      </template>
    </div>

    <!-- 通知弹框 -->
    <NotificationModal ref="notificationModalRef" />

    <!-- 开发者工具（仅开发环境显示） -->
    <div
      v-if="isDev"
      class="fixed bottom-4 right-4 bg-gray-800 text-white p-2 rounded-lg text-xs z-50"
    >
      <button
        @click="resetNotificationStatus"
        class="bg-red-500 hover:bg-red-600 px-2 py-1 rounded text-white"
      >
        重置通知状态
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useActivityStore } from '@/stores/activity'
import LoadingSpinner from '@/components/LoadingSpinner.vue'
import NotificationModal from '@/components/NotificationModal.vue'
import type { ActivityStatus, ResultStatus } from '@/types'
import { formatAmount } from '@/utils/format'

const router = useRouter()
const activityStore = useActivityStore()
const searchKeyword = ref('')
const notificationModalRef = ref<InstanceType<typeof NotificationModal>>()

// 模拟未读通知状态
const hasUnreadNotifications = ref(true)

// 开发环境检查
const isDev = process.env.NODE_ENV === 'development'

// 过滤后的活动列表
const filteredActivities = computed(() => {
  if (!searchKeyword.value.trim()) {
    // 如果没有搜索关键词，显示所有活动
    return activityStore.activities
  }

  const keyword = searchKeyword.value.toLowerCase()
  // 搜索时只显示已公布结果的活动
  return activityStore.activities.filter(activity =>
    activity.resultStatus === 'published' && (
      activity.name.toLowerCase().includes(keyword) ||
      activity.description.toLowerCase().includes(keyword)
    )
  )
})

// 跳转到活动详情
const goToActivity = (activityId: string) => {
  router.push(`/activity/${activityId}`)
}

// 过滤活动
const filterActivities = () => {
  // 实时过滤，不需要额外处理
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
}

// 显示通知弹框
const showNotifications = () => {
  if (notificationModalRef.value) {
    notificationModalRef.value.showModal()
    // 点击后清除未读状态
    hasUnreadNotifications.value = false
  }
}

// 分享当前页面
const shareCurrentPage = async () => {
  try {
    const currentUrl = window.location.href

    // 尝试使用现代浏览器的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(currentUrl)
      showToast('链接已复制到剪贴板')
    } else {
      // 降级方案：使用传统的复制方法
      const textArea = document.createElement('textarea')
      textArea.value = currentUrl
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        document.execCommand('copy')
        showToast('链接已复制到剪贴板')
      } catch (err) {
        console.error('复制失败:', err)
        showToast('复制失败，请手动复制链接')
      } finally {
        document.body.removeChild(textArea)
      }
    }
  } catch (err) {
    console.error('分享失败:', err)
    showToast('分享失败，请重试')
  }
}

// 显示提示信息
const showToast = (message: string) => {
  // 创建提示元素
  const toast = document.createElement('div')
  toast.textContent = message
  toast.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 text-white px-4 py-2 rounded-lg text-sm z-50 transition-all duration-300'

  document.body.appendChild(toast)

  // 显示动画
  setTimeout(() => {
    toast.style.opacity = '1'
    toast.style.transform = 'translate(-50%, 0)'
  }, 10)

  // 3秒后移除
  setTimeout(() => {
    toast.style.opacity = '0'
    toast.style.transform = 'translate(-50%, -20px)'
    setTimeout(() => {
      if (document.body.contains(toast)) {
        document.body.removeChild(toast)
      }
    }, 300)
  }, 3000)
}

// 重置通知状态（开发者工具）
const resetNotificationStatus = () => {
  if (notificationModalRef.value) {
    notificationModalRef.value.clearNotificationStatus()
    showToast('通知状态已重置，刷新页面后将重新显示通知')
  }
}

// 获取状态样式类
const getStatusClass = (status: ActivityStatus) => {
  return status === 'active' 
    ? 'bg-green-100 text-green-800' 
    : 'bg-gray-100 text-gray-600'
}

// 获取状态图标
const getStatusIcon = (status: ActivityStatus) => {
  return status === 'active' ? 'fas fa-play' : 'fas fa-check'
}

// 获取状态文本
const getStatusText = (status: ActivityStatus) => {
  return status === 'active' ? '进行中' : '已结束'
}

// 获取状态颜色
const getStatusColor = (status: ActivityStatus) => {
  return status === 'active' ? 'text-green-600' : 'text-gray-600'
}

// 获取中奖结果公布状态文本
const getResultStatusText = (resultStatus: ResultStatus) => {
  return resultStatus === 'published' ? '已公布' : '未公布'
}

// 获取中奖结果公布状态颜色
const getResultStatusColor = (resultStatus: ResultStatus) => {
  return resultStatus === 'published' ? 'text-green-600' : 'text-orange-600'
}

// 页面初始化
onMounted(async () => {
  // 加载活动数据
  await activityStore.fetchActivities()

  // 默认打开系统通知（延迟1秒显示，但只在本次会话未显示过时显示）
  setTimeout(() => {
    if (notificationModalRef.value && notificationModalRef.value.shouldShowNotification()) {
      showNotifications()
    }
  }, 1000)
})
</script>

<style scoped>
.activity-icon {
  @apply w-12 h-12 bg-gradient-to-r from-legal-red-500 to-legal-red-600 rounded-xl flex items-center justify-center;
}

.status-badge {
  @apply px-3 py-1 rounded-full text-xs font-medium;
}

.stats-grid {
  @apply grid grid-cols-3 gap-4 mt-4;
}

.stat-item {
  @apply text-center p-3 bg-gray-50 rounded-lg;
}
</style>
