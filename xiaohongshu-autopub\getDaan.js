/* 获取答案 */
/* 
http://www.vjianghu.cn/dati1/yzdd-844.html?wxid=123456&wid=173&date=250507
http://www.vjianghu.cn/dati1/yzddkz-844.html
*/
const axios = require('axios');
const cheerio = require('cheerio');
const https = require('https');
const dayjs = require('dayjs');
const fs = require('fs');


const questionId = [847];
const wxId = "123456";
const wid = "173";
const date = "250507";



// 1. 获取cookie
async function getCookie(quesId) {
  const url = `http://www.vjianghu.cn/dati1/yzdd-${quesId}.html`;
  const params = {
    wxid: wxId,
    wid: wid,
    date: date
  };

  const headers = {
    Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "zh-CN,zh;q=0.9,en-CN;q=0.8,en;q=0.7", 
    "Cache-Control": "no-cache",
    Connection: "keep-alive",
    Pragma: "no-cache",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Mobile Safari/537.36 MicroMessenger/7.0.20.1781(0x2700143B) NetType/WIFI Language/zh_CN"
  };

  try {
    const response = await axios.get(url, {
      params,
      headers,
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    });

    const cookies = response.headers["set-cookie"];
    if (cookies && cookies.length > 0) {
      return cookies.join("; ");
    }
    return "";
  } catch (error) {
    console.error("获取Cookie失败:", error);
    return "";
  }
}

// 2. 获取答案
async function getAnswer(questionId) {
  const cookie = await getCookie(questionId);
  const url = `http://www.vjianghu.cn/dati1/yzddkz-${questionId}.html`;
  const response = await axios.get(url, {
    headers: {
      Cookie: cookie
    }
  });
  const $ = cheerio.load(response.data);
  const tk = $('.tk');
  const answers = [];
  tk.each((index, element) => {
    const $tk = $(element);
    const options = $tk.find('.option');
    let correctAnswer = '';
    
    options.each((i, opt) => {
      if($(opt).attr('data-zq') === '1') {
        correctAnswer = String.fromCharCode(65 + i); // Convert 0->A, 1->B etc
      }
    });

    answers.push({
      questionNum: index + 1,
      answer: correctAnswer
    });
    
    console.log(`第${index + 1}题答案是: ${correctAnswer}`);
  });
  return answers;
}

// 3. 执行
async function main() {
  const day = dayjs().date();
  const month = dayjs().month() + 1; // dayjs月份从0开始
  const date = month < 10 ? `${month}月${day < 10 ? day : day}日` : `${month}月${day < 10 ? '0' + day : day}日`;
  const title = `${date}答案，答题抽 50 元话费，宝子速来！`;
  let jsonData = {
    title:title,
    date:date,
    answer:'',
    img:`C:/Users/<USER>/Desktop/pufa/${date}.png`
  }
    let answerStr = '';
    for(let i = 0; i < questionId.length; i++) {
        const answers = await getAnswer(questionId[i]);
        const answerLetters = answers.map(a => a.answer).join('');
        answerStr += `${answerLetters};`;
        jsonData.answer = answerLetters;
    }
    fs.writeFileSync('./answer.json', JSON.stringify(jsonData, null, 2));
    // console.log(answerStr.trim()); // 输出 "844:BABAA; 866:AABAB"
    // send.sendNotify(`${answerStr.trim()}`,'问卷答案(844,846): ');
}

main();

