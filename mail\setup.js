const fs = require('fs');
const path = require('path');
const readline = require('readline');

/**
 * 邮件系统快速设置脚本
 */
class EmailSetup {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        this.envPath = path.join(__dirname, '.env');
        this.examplePath = path.join(__dirname, '.env.example');
    }

    /**
     * 询问用户输入
     */
    question(prompt) {
        return new Promise((resolve) => {
            this.rl.question(prompt, resolve);
        });
    }

    /**
     * 检查文件是否存在
     */
    fileExists(filePath) {
        try {
            fs.accessSync(filePath, fs.constants.F_OK);
            return true;
        } catch (err) {
            return false;
        }
    }

    /**
     * 显示163邮箱授权码获取指南
     */
    showAuthCodeGuide() {
        console.log('\n📖 163邮箱授权码获取指南:');
        console.log('='.repeat(50));
        console.log('1. 登录163邮箱网页版 (mail.163.com)');
        console.log('2. 点击右上角"设置" → "POP3/SMTP/IMAP"');
        console.log('3. 开启"SMTP服务"');
        console.log('4. 点击"生成授权码"');
        console.log('5. 按提示发送短信验证');
        console.log('6. 复制生成的授权码');
        console.log('='.repeat(50));
    }

    /**
     * 交互式配置
     */
    async interactiveSetup() {
        console.log('🚀 邮件系统快速设置向导');
        console.log('='.repeat(50));

        // 检查是否已存在配置文件
        if (this.fileExists(this.envPath)) {
            const overwrite = await this.question('⚠️  .env 文件已存在，是否覆盖？(y/N): ');
            if (overwrite.toLowerCase() !== 'y') {
                console.log('❌ 设置已取消');
                return false;
            }
        }

        console.log('\n📧 请输入邮件配置信息:');

        // 发送者邮箱
        const senderEmail = await this.question('发送者邮箱 (默认: <EMAIL>): ') || '<EMAIL>';
        
        // 发送者姓名
        const senderName = await this.question('发送者姓名 (默认: Wei Cracker): ') || 'Wei Cracker';
        
        // 显示授权码获取指南
        this.showAuthCodeGuide();
        
        // 授权码
        const senderPassword = await this.question('163邮箱授权码 (必填): ');
        if (!senderPassword.trim()) {
            console.log('❌ 授权码不能为空！');
            return false;
        }
        
        // 接收者邮箱
        const recipientEmail = await this.question('接收者邮箱 (默认: <EMAIL>): ') || '<EMAIL>';
        
        // 接收者姓名
        const recipientName = await this.question('接收者姓名 (默认: Howard): ') || 'Howard';

        // 生成配置内容
        const envContent = `# 邮件发送配置
# 由快速设置向导生成于 ${new Date().toLocaleString()}

# 发送者邮箱配置
SENDER_EMAIL=${senderEmail}
SENDER_PASSWORD=${senderPassword}
SENDER_NAME=${senderName}

# 接收者邮箱配置
RECIPIENT_EMAIL=${recipientEmail}
RECIPIENT_NAME=${recipientName}

# 注意事项:
# 1. 请妥善保管此文件，不要泄露授权码
# 2. 此文件已自动添加到 .gitignore 中
# 3. 如需修改配置，可直接编辑此文件或重新运行设置向导
`;

        try {
            // 写入配置文件
            fs.writeFileSync(this.envPath, envContent, 'utf8');
            console.log('\n✅ 配置文件已生成: .env');
            
            return {
                senderEmail,
                senderName,
                recipientEmail,
                recipientName
            };
        } catch (error) {
            console.error('❌ 配置文件写入失败:', error.message);
            return false;
        }
    }

    /**
     * 测试配置
     */
    async testConfiguration() {
        console.log('\n🔍 测试邮件配置...');
        
        try {
            // 加载环境变量
            if (this.fileExists(this.envPath)) {
                const envContent = fs.readFileSync(this.envPath, 'utf8');
                const envLines = envContent.split('\n');
                
                for (const line of envLines) {
                    if (line.includes('=') && !line.startsWith('#')) {
                        const [key, value] = line.split('=');
                        if (key && value) {
                            process.env[key.trim()] = value.trim();
                        }
                    }
                }
            }

            // 运行测试脚本
            const { spawn } = require('child_process');
            const testProcess = spawn('node', ['test-config.js'], {
                cwd: __dirname,
                stdio: 'inherit'
            });

            return new Promise((resolve) => {
                testProcess.on('close', (code) => {
                    resolve(code === 0);
                });
            });

        } catch (error) {
            console.error('❌ 测试配置时出错:', error.message);
            return false;
        }
    }

    /**
     * 显示完成信息
     */
    showCompletionInfo(config) {
        console.log('\n🎉 邮件系统设置完成！');
        console.log('='.repeat(50));
        console.log('📋 配置摘要:');
        console.log(`发送者: ${config.senderName} <${config.senderEmail}>`);
        console.log(`接收者: ${config.recipientName} <${config.recipientEmail}>`);
        console.log('\n💡 使用方法:');
        console.log('1. 发送测试邮件: npm run mail');
        console.log('2. 测试配置: npm run mail-test');
        console.log('3. 查看示例: node mail/example.js');
        console.log('\n📚 更多信息请查看: mail/README.md');
        console.log('='.repeat(50));
    }

    /**
     * 关闭输入接口
     */
    close() {
        this.rl.close();
    }
}

/**
 * 主函数
 */
async function main() {
    const setup = new EmailSetup();
    
    try {
        // 交互式设置
        const config = await setup.interactiveSetup();
        
        if (!config) {
            console.log('❌ 设置失败或已取消');
            process.exit(1);
        }

        // 测试配置
        const testSuccess = await setup.testConfiguration();
        
        if (testSuccess) {
            setup.showCompletionInfo(config);
        } else {
            console.log('\n⚠️  配置已保存，但测试失败');
            console.log('💡 请检查授权码是否正确，或稍后运行 "npm run mail-test" 重新测试');
        }

    } catch (error) {
        console.error('❌ 设置过程中出错:', error.message);
        process.exit(1);
    } finally {
        setup.close();
    }
}

// 如果直接运行此文件，则执行设置向导
if (require.main === module) {
    main();
}
