@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #fbbf24 100%);
    min-height: 100vh;
  }
}

@layer components {
  .status-bar {
    @apply h-12 bg-white bg-opacity-10 backdrop-blur-lg flex justify-between items-center px-6 text-sm font-semibold text-white;
  }
  
  .nav-bar {
    @apply bg-white bg-opacity-10 backdrop-blur-lg;
  }
  
  .activity-card {
    @apply bg-white rounded-2xl mx-4 my-4 p-5 shadow-lg border-l-4 border-legal-red-500 transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }

  .winner-card {
    @apply bg-white rounded-2xl mx-5 my-2 p-4 shadow-md border-l-4 border-legal-gold-400;
  }

  .result-card {
    @apply bg-white rounded-2xl mx-5 my-3 p-5 shadow-lg border-l-4 border-legal-gold-400;
  }

  .search-bar {
    @apply bg-white bg-opacity-90 backdrop-blur-lg mx-5 my-4 p-3 rounded-xl flex items-center;
  }

  .search-input {
    @apply flex-1 border-none bg-transparent text-base outline-none;
  }

  .stats-container {
    @apply bg-white bg-opacity-90 backdrop-blur-lg mx-5 my-4 p-4 rounded-xl grid grid-cols-3 gap-4 text-center;
  }
  
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 opacity-0 invisible transition-all duration-300;
  }
  
  .modal-overlay.show {
    @apply opacity-100 visible;
  }
  
  .modal-content {
    @apply bg-white rounded-3xl p-6 mx-5 max-w-sm w-full text-center transform scale-75 transition-transform duration-300 shadow-2xl;
  }
  
  .modal-overlay.show .modal-content {
    @apply scale-100;
  }
  
  .xiaohongshu-icon {
    @apply w-16 h-16 bg-gradient-to-r from-xiaohongshu-500 to-xiaohongshu-600 rounded-2xl flex items-center justify-center mx-auto mb-4;
  }
  
  .modal-btn {
    @apply bg-gradient-to-r from-xiaohongshu-500 to-xiaohongshu-600 text-white border-none py-3 px-6 rounded-full text-sm font-semibold mx-2 transition-all duration-300 cursor-pointer;
  }
  
  .modal-btn.secondary {
    @apply bg-gray-100 text-gray-600;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-white;
  }
}
