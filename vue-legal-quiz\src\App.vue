<template>
  <div id="app" class="min-h-screen">
    <!-- iOS 状态栏 -->
   <!--  <div class="status-bar">
      <div class="flex items-center space-x-1">
        <span>9:41</span>
      </div>
      <div class="flex items-center space-x-1">
        <i class="fas fa-signal text-sm"></i>
        <i class="fas fa-wifi text-sm"></i>
        <i class="fas fa-battery-three-quarters text-sm"></i>
      </div>
    </div> -->

    <!-- 路由视图 -->
    <router-view />

    <!-- 小红书推广弹框 -->
    <PromotionModal />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import PromotionModal from '@/components/PromotionModal.vue'
import { useActivityStore } from '@/stores/activity'

const activityStore = useActivityStore()

onMounted(() => {
  // 初始化数据
  activityStore.fetchActivities()
})
</script>
