const axios = require('axios')
const sendMessage = require('../utils/sendmsg')
const iconv = require('iconv-lite')

// 获取外部变量
const PJ_COOKIE = process.env.PJ_COOKIE
// https://github.com/mengshouer/CheckinBox/blob/master/Checkin52pj/Checkin52pjForSCF.py
let headersRef = {
    'Accept': ' text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
    'Accept-Encoding': ' gzip, deflate, br',
    'Accept-Language': ' zh-CN,zh;q=0.9,en-CN;q=0.8,en;q=0.7',
    'Connection': ' keep-alive',
    'Cookie': '',
    'Host': ' www.52pojie.cn',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua': '"Chromium";v="92", " Not A;Brand";v="99", "Google Chrome";v="92"',
    'Sec-Fetch-Dest': ' document',
    'Sec-Fetch-Mode': ' navigate',
    'Sec-Fetch-Site': ' none',
    'Sec-Fetch-User': ' ?1',
    'Upgrade-Insecure-Requests': ' 1',
    'User-Agent': ' Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36'
}

function initAxios() {
    const instance = axios.create({
        withCredentials: true,
        responseType: 'arraybuffer',
    });
    instance.interceptors.response.use(function (response) {
        response.data = iconv.decode(response.data, 'gbk')
        return response;
    }, function (error) {
        return Promise.reject(error);
    });
    return instance
}

async function start() {
    if (PJ_COOKIE) {
        headersRef['Cookie'] = PJ_COOKIE
        let myaxios = initAxios(headersRef)
        // 发起签到任务
        let config = {
            method: 'get',
            url: 'http://www.52pojie.cn/home.php?mod=task&do=apply&id=2',
            headers: headersRef
        };
        let config2 = {
            method: 'get',
            url: 'http://www.52pojie.cn/home.php?mod=task&do=draw&id=2',
            headers: headersRef
        };

        let ret;
        try {
            await myaxios(config)
        } catch (error) {
            console.log('1---', error)
        }
        try {
            ret = await myaxios(config2)
        } catch (error) {
            console.log('2---', error)
        }

        let retMsg = ''
        if (ret.data.search('不是进行中的任务') > -1) {
            retMsg = '不是进行中的任务'
        } else if (ret.data.search('您需要先登录才能继续本操作') > -1) {
            retMsg = 'Cookie失效'
        } else if (ret.data.search('恭喜') > -1) {
            retMsg = '签到成功'
        } else {
            retMsg = '签到失败'
        }
        sendMessage(`52pojie签到：\n${retMsg}`)
    } else {
        let errmsg = '请提前配置【PJ_COOKIE】'
        console.log(errmsg)
        sendMessage(`52pojie签到：\n${errmsg}`)
    }
}

try {
    start()
} catch (error) {
    sendMessage(`52破解签到失败：${error}`);
}