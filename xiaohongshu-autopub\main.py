import datetime
import json
from time import sleep

from playwright.sync_api import sync_playwright

from xhs import DataFetchError, XhsClient


def sign(uri, data=None, a1="", web_session=""):
    for _ in range(10):
        try:
            with sync_playwright() as playwright:
                stealth_js_path = "./stealth.min.js"
                chromium = playwright.chromium

                # 如果一直失败可尝试设置成 False 让其打开浏览器，适当添加 sleep 可查看浏览器状态
                browser = chromium.launch(headless=True)

                browser_context = browser.new_context()
                browser_context.add_init_script(path=stealth_js_path)
                context_page = browser_context.new_page()
                context_page.goto("https://www.xiaohongshu.com")
                browser_context.add_cookies([
                    {'name': 'a1', 'value': a1, 'domain': ".xiaohongshu.com", 'path': "/"}]
                )
                context_page.reload()
                # 这个地方设置完浏览器 cookie 之后，如果这儿不 sleep 一下签名获取就失败了，如果经常失败请设置长一点试试
                sleep(1)
                encrypt_params = context_page.evaluate("([url, data]) => window._webmsxyw(url, data)", [uri, data])
                return {
                    "x-s": encrypt_params["X-s"],
                    "x-t": str(encrypt_params["X-t"])
                }
        except Exception:
            # 这儿有时会出现 window._webmsxyw is not a function 或未知跳转错误，因此加一个失败重试趴
            pass
    raise Exception("重试了这么多次还是无法签名成功，寄寄寄")
def get_answer():
    with open('./answer.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
        print(data)
    return data
def test_create_simple_note(xhs_client: XhsClient):
    answerJson = get_answer()
    title = answerJson['title']
    desc = f"""
   快来呀！！ {answerJson['date']}🎉 参加中国普法民法典有奖竞答就有机会🉐 50 元话费！，今天答案已整理好👇 速冲！	
 \n\n\t\n\n\n
📣 民法典今日答案（{answerJson['date']}）
👉 {answerJson['answer']} （直接抄！）	
 \n\n\t\n\n\n
🎯 活动时间轴：
✅ 第一阶段：5.1-5.10
✅ 第二阶段：5.11-5.20
✅ 第三阶段：5.21-5.30	
 \n\n\t\n\n\n
🎁 超高中奖率：
每个阶段，每天5道题，累计答满7天即可，就有机会抽取50元话费！ 共600个中奖机会！小伙伴们不要错过啦！
 \n\n\t\n\n\n
💡 参与方式：
关注 中国普法 GZH ，点击菜单栏“齐参与”，找到“民法典有奖竞答”进入答题！手快有手慢无，冲鸭！！
 \n\n\t\n\n\n
 #中国普法[话题]# #中国普法答案[话题]# #中国普法今日答题答案[话题]# #答题抽奖[话题]# #话费抽奖[话题]# #免费话费[话题]#  
    """
    images = [
        answerJson['img'],
    ]
    # 话题链接
    topics = [
        {
        "id": "614141f400000000010021c3",
        "name": "中国普法",
        "link": "",
        "type": "topic"
      },
      {
        "id": "6557ffd8000000000f035c8d",
        "name": "中国普法答案",
        "link": "",
        "type": "topic"
      },
      {
        "id": "659b6324000000003f03d46f",
        "name": "中国普法今日答题答案",
        "link": "",
        "type": "topic"
      },
      {
        "id": "61cfc8a00000000001003591",
        "name": "答题抽奖",
        "link": "",
        "type": "topic"
      },
      {
        "id": "6270c3d7000000000101cfb6",
        "name": "话费抽奖",
        "link": "",
        "type": "topic"
      },
      {
        "id": "5cfefe43000000000d022e49",
        "name": "免费话费",
        "link": "",
        "type": "topic"
      }
    ]
    ats = []
    note = xhs_client.create_image_note(title, desc, images, ats=ats, topics=topics, is_private=False,
                                        post_time="2023-07-25 23:59:59")
    print(note)

if __name__ == '__main__':
    
    mycookie = "a1=187d2defea8dz1fgwydnci40kw265ikh9fsxn66qs50000726043;webId=ba57f42593b9e55840a289fa0b755374;gid.sign=PSF1M3U6EBC/Jv6eGddPbmsWzLI=;gid=yYWfJfi820jSyYWfJfdidiKK0YfuyikEvfISMAM348TEJC28K23TxI888WJK84q8S4WfY2Sy;acw_tc=0ad629c517467788120536627e8ebe25bc9116d7fea4a4fdcaa63e843b9b7d;web_session=040069b1f2f94956e9f1bef2283a4b826525cd"
    xhs_client = XhsClient(cookie=mycookie,sign=sign)
    print(datetime.datetime.now())
    # xhs_client.cookie = mycookie

    # print(xhs_client.get_self_info())
    
    test_create_simple_note(xhs_client)