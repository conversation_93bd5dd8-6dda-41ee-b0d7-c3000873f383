# 邮件发送配置示例
# 复制此文件为 .env 并填入真实的配置信息

# 发送者邮箱配置
SENDER_EMAIL=<EMAIL>
SENDER_PASSWORD=your_163_email_auth_code_here
SENDER_NAME=Wei Cracker

# 接收者邮箱配置
RECIPIENT_EMAIL=<EMAIL>
RECIPIENT_NAME=Howard

# 注意事项:
# 1. SENDER_PASSWORD 应该是163邮箱的授权码，不是登录密码
# 2. 获取163邮箱授权码的步骤：
#    - 登录163邮箱网页版
#    - 进入设置 -> POP3/SMTP/IMAP
#    - 开启SMTP服务
#    - 生成授权码
# 3. 请妥善保管授权码，不要泄露给他人
# 4. .env 文件应该添加到 .gitignore 中，避免提交到版本控制系统
