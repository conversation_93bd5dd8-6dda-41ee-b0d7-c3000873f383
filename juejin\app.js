"use strict"

/*---------------依赖-----------------*/
const axios = require('axios');
const send = require('../sendNotify.js')

/*---------------配置-----------------*/
// 获取外部变量
const J<PERSON>_<PERSON><PERSON><PERSON> = process.env.JJ_COOKIE || '_tea_utm_cache_2608=undefined; __tea_cookie_tokens_2608=%257B%2522web_id%2522%253A%25227274841890574124601%2522%252C%2522user_unique_id%2522%253A%25227274841890574124601%2522%252C%2522timestamp%2522%253A1693806132398%257D; n_mh=71ZVHvMpJR068y0cE-uWzeHwXwGnKOgsi_4VqBrqV40; sid_guard=f5190ca21239e00cf192263b6813ede7%7C1694501376%7C31535999%7CWed%2C+11-Sep-2024+06%3A49%3A35+GMT; uid_tt=0a7850c94af95325a64612805fd49053; uid_tt_ss=0a7850c94af95325a64612805fd49053; sid_tt=f5190ca21239e00cf192263b6813ede7; sessionid=f5190ca21239e00cf192263b6813ede7; sessionid_ss=f5190ca21239e00cf192263b6813ede7; sid_ucp_v1=1.0.0-KDQ3OWU1MDM0ZjI0NzBlMTZkNmZjZjY4OWYyNTE0Mzg1NmVmZDE1OTgKFgjO-tC-_fX5BhCAlICoBhiwFDgIQAEaAmhsIiBmNTE5MGNhMjEyMzllMDBjZjE5MjI2M2I2ODEzZWRlNw; ssid_ucp_v1=1.0.0-KDQ3OWU1MDM0ZjI0NzBlMTZkNmZjZjY4OWYyNTE0Mzg1NmVmZDE1OTgKFgjO-tC-_fX5BhCAlICoBhiwFDgIQAEaAmhsIiBmNTE5MGNhMjEyMzllMDBjZjE5MjI2M2I2ODEzZWRlNw; store-region=cn-ln; store-region-src=uid; _ga=GA1.2.834362743.1694501379; csrf_session_id=eb190723d9e6db7ae5b0a1590c21dd0f; msToken=3hUB-x_YY7qMoIWaAkH2tbnEdHRb2cvHhWyBhFkQ4vvWd2ZvcFh-LCkkUnQsS4FYG2px3BVELECkduuGjv_oXmwbSqm_Bw6AfqBTD08jxhcj5bXV6AIE1UJksyveUqf8';


const config = {
    "baseUrl": "https://api.juejin.cn",
    "apiUrl": {
        "getTodayStatus": "/growth_api/v1/get_today_status",
        "checkIn": "/growth_api/v1/check_in",
        "getLotteryConfig": "/growth_api/v1/lottery_config/get",
        "drawLottery": "/growth_api/v1/lottery/draw"
    },
    "cookie": JJ_COOKIE,
    "email": {
        "qq": {
            "user": "<EMAIL>",
            "from": "<EMAIL>",
            "to": "<EMAIL>",
            "pass": ""
        }
    }
}

/*---------------掘金-----------------*/

// 签到
const checkIn = async () => {
    let { error, isCheck } = await getTodayCheckStatus();
    if (error) return console.log('查询签到失败');
    if (isCheck) return console.log('今日已参与签到');
    const { cookie, baseUrl, apiUrl } = config;
    let { data } = await axios({ url: baseUrl + apiUrl.checkIn, method: 'post', headers: { Cookie: cookie } });
    if (data.err_no) {
        console.log('签到失败');
        await send.sendNotify('今日掘金签到：失败');
    } else {
        console.log(`签到成功！当前积分：${data.data.sum_point}`);
        await send.sendNotify('今日掘金签到：成功');
    }
}

// 查询今日是否已经签到
const getTodayCheckStatus = async () => {
    const { cookie, baseUrl, apiUrl } = config;
    let { data } = await axios({ url: baseUrl + apiUrl.getTodayStatus, method: 'get', headers: { Cookie: cookie } });
    if (data.err_no) {
        await send.sendNotify('今日掘金签到查询：失败');
    }
    return { error: data.err_no !== 0, isCheck: data.data }
}

// 抽奖
const draw = async () => {
    let { error, isDraw } = await getTodayDrawStatus();
    if (error) return console.log('查询抽奖次数失败');
    if (isDraw) return console.log('今日已无免费抽奖次数');
    const { cookie, baseUrl, apiUrl } = config;
    let { data } = await axios({ url: baseUrl + apiUrl.drawLottery, method: 'post', headers: { Cookie: cookie } });
    if (data.err_no) return console.log('免费抽奖失败');
    console.log(`恭喜抽到：${data.data.lottery_name}`);
    await send.sendNotify(`恭喜抽到：${data.data.lottery_name}`);
}

// 获取今天免费抽奖的次数
const getTodayDrawStatus = async () => {
    const { cookie, baseUrl, apiUrl } = config;
    let { data } = await axios({ url: baseUrl + apiUrl.getLotteryConfig, method: 'get', headers: { Cookie: cookie } });
    if (data.err_no) {
        return { error: true, isDraw: false }
    } else {
        return { error: false, isDraw: data.data.free_count === 0 }
    }
}


/* exports.juejin = async (event, context) => {
    console.log('开始');
    await checkIn();
    await draw();
    console.log('结束');
}; */


(async () => {
    console.log('开始');
    await checkIn();
    await draw();
    console.log('结束');
})()