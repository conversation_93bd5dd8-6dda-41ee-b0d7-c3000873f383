<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索结果</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #fbbf24 100%);
            min-height: 100vh;
        }
        .status-bar {
            height: 47px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 14px;
            font-weight: 600;
            color: white;
        }
        .result-card {
            background: white;
            border-radius: 16px;
            margin: 12px 20px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-left: 4px solid #fbbf24;
        }
        .search-bar {
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            margin: 16px 20px;
            padding: 12px 16px;
            border-radius: 12px;
            display: flex;
            align-items: center;
        }
        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            font-size: 16px;
            outline: none;
        }
        .activity-tag {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 8px;
        }
        .prize-info {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: white;
            padding: 12px 16px;
            border-radius: 12px;
            margin-top: 12px;
        }
        .stats-container {
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            margin: 16px 20px;
            padding: 16px;
            border-radius: 12px;
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        .user-avatar {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #dc2626 0%, #fbbf24 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
        }
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 24px;
            margin: 20px;
            max-width: 300px;
            width: 100%;
            text-align: center;
            transform: scale(0.8);
            transition: transform 0.3s ease;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        .modal-overlay.show .modal-content {
            transform: scale(1);
        }
        .xiaohongshu-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
        }
        .modal-btn {
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .modal-btn.secondary {
            background: #f3f4f6;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <i class="fas fa-battery-three-quarters text-sm"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg">
        <div class="flex items-center justify-between p-4">
            <i class="fas fa-arrow-left text-white text-lg"></i>
            <h1 class="text-white text-lg font-semibold">搜索结果</h1>
            <i class="fas fa-filter text-white text-lg"></i>
        </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
        <i class="fas fa-search text-gray-400 mr-3"></i>
        <input type="text" class="search-input" placeholder="张三" value="张三">
        <i class="fas fa-times text-gray-400 ml-3"></i>
    </div>

    <!-- 统计信息 -->
    <div class="stats-container">
        <div>
            <div class="text-2xl font-bold text-red-600">3</div>
            <div class="text-xs text-gray-600">找到结果</div>
        </div>
        <div>
            <div class="text-2xl font-bold text-yellow-600">3</div>
            <div class="text-xs text-gray-600">中奖次数</div>
        </div>
        <div>
            <div class="text-2xl font-bold text-green-600">¥150</div>
            <div class="text-xs text-gray-600">总奖金</div>
        </div>
    </div>

    <!-- 结果列表 -->
    <div class="flex-1 pb-6">
        <!-- 搜索结果1 -->
        <div class="result-card">
            <div class="activity-tag">
                <i class="fas fa-gavel mr-1"></i>
                2024年宪法宣传周答题活动
            </div>
            
            <div class="flex items-start justify-between">
                <div class="flex items-center">
                    <div class="user-avatar">
                        <i class="fas fa-user text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-800">张三</h3>
                        <p class="text-gray-600 text-sm">138****5678</p>
                        <p class="text-xs text-gray-500 mt-1">
                            <i class="fas fa-calendar mr-1"></i>
                            中奖时间：2024-12-03 14:30
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="prize-info">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm opacity-90">奖品</p>
                        <p class="font-bold">50元话费</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm opacity-90">状态</p>
                        <p class="font-bold">
                            <i class="fas fa-check-circle mr-1"></i>
                            已发放
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索结果2 -->
        <div class="result-card">
            <div class="activity-tag" style="background: linear-gradient(135deg, #059669 0%, #047857 100%);">
                <i class="fas fa-shield-alt mr-1"></i>
                民法典知识竞赛
            </div>
            
            <div class="flex items-start justify-between">
                <div class="flex items-center">
                    <div class="user-avatar" style="background: linear-gradient(135deg, #059669 0%, #fbbf24 100%);">
                        <i class="fas fa-user text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-800">张三</h3>
                        <p class="text-gray-600 text-sm">138****5678</p>
                        <p class="text-xs text-gray-500 mt-1">
                            <i class="fas fa-calendar mr-1"></i>
                            中奖时间：2024-11-28 16:45
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="prize-info" style="background: linear-gradient(135deg, #059669 0%, #047857 100%);">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm opacity-90">奖品</p>
                        <p class="font-bold">50元话费</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm opacity-90">状态</p>
                        <p class="font-bold">
                            <i class="fas fa-check-circle mr-1"></i>
                            已发放
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索结果3 -->
        <div class="result-card">
            <div class="activity-tag" style="background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);">
                <i class="fas fa-university mr-1"></i>
                法治政府建设知识问答
            </div>
            
            <div class="flex items-start justify-between">
                <div class="flex items-center">
                    <div class="user-avatar" style="background: linear-gradient(135deg, #6b7280 0%, #fbbf24 100%);">
                        <i class="fas fa-user text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-800">张三</h3>
                        <p class="text-gray-600 text-sm">138****5678</p>
                        <p class="text-xs text-gray-500 mt-1">
                            <i class="fas fa-calendar mr-1"></i>
                            中奖时间：2024-10-15 10:20
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="prize-info" style="background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm opacity-90">奖品</p>
                        <p class="font-bold">50元话费</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm opacity-90">状态</p>
                        <p class="font-bold">
                            <i class="fas fa-check-circle mr-1"></i>
                            已发放
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 相关搜索建议 -->
        <div class="bg-white bg-opacity-90 backdrop-filter backdrop-blur-lg mx-5 my-4 p-4 rounded-16">
            <h3 class="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                相关搜索
            </h3>
            <div class="flex flex-wrap gap-2">
                <button class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm" onclick="searchRelated('张三丰')">
                    张三丰
                </button>
                <button class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm" onclick="searchRelated('138****')">
                    138****
                </button>
                <button class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm" onclick="searchRelated('宪法')">
                    宪法活动
                </button>
            </div>
        </div>
    </div>

    <!-- 小红书推广弹框 -->
    <div class="modal-overlay" id="promotionModal">
        <div class="modal-content">
            <div class="xiaohongshu-icon">
                <i class="fab fa-instagram text-white text-2xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-800 mb-2">关注小红书获取更多</h3>
            <p class="text-sm text-gray-600 mb-4">最新普法资讯 · 答题答案</p>
            <div class="flex justify-center">
                <button class="modal-btn" onclick="followXiaohongshu()">
                    <i class="fab fa-instagram mr-2"></i>
                    立即关注
                </button>
                <button class="modal-btn secondary" onclick="closeModal()">
                    稍后再说
                </button>
            </div>
        </div>
    </div>



    <script>
        // 页面加载时显示推广弹框
        window.addEventListener('load', function() {
            setTimeout(() => {
                showModal();
            }, 2000); // 2秒后显示弹框
        });

        // 显示弹框
        function showModal() {
            const modal = document.getElementById('promotionModal');
            modal.classList.add('show');
        }

        // 关闭弹框
        function closeModal() {
            const modal = document.getElementById('promotionModal');
            modal.classList.remove('show');
        }

        // 关注小红书
        function followXiaohongshu() {
            console.log('跳转到小红书关注页面');
            alert('即将跳转到小红书关注页面');
            closeModal();
        }

        // 点击遮罩层关闭弹框
        document.getElementById('promotionModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 搜索功能
        document.querySelector('.search-input').addEventListener('input', function(e) {
            console.log('搜索:', e.target.value);
        });

        // 清除搜索
        document.querySelector('.fa-times').addEventListener('click', function() {
            document.querySelector('.search-input').value = '';
        });

        // 相关搜索
        function searchRelated(keyword) {
            document.querySelector('.search-input').value = keyword;
            console.log('相关搜索:', keyword);
        }

        // 结果卡片点击
        document.querySelectorAll('.result-card').forEach(card => {
            card.addEventListener('click', function() {
                console.log('查看详情:', this.querySelector('h3').textContent);
            });
        });
    </script>
</body>
</html>
