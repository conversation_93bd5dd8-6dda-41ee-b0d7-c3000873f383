/*
 * @Author: jiwei
 * @Date: 2023-11-02 09:45:14
 * @LastEditors: jiwei
 * @LastEditTime: 2024-01-12 09:32:34
 * @Description: 龙湖 每日自动签到、自动抽奖、答题
 */
var axios = require('axios');
var dayjs = require('dayjs');
const send = require("../sendNotify.js")

let LFUserToken = process.env.LF_USERTOKEN || 'fc1f6572c6c0422ba93711cd3a92069a'
let GY_USERTOKEN = process.env.GY_USERTOKEN || '32e1d0db68004baf9f09e26237f5ada1'
// 抽奖活动ID
const activity_no = "11111111111703831890354072540000"
// 话题赚龙珠TASKID
let task_id = 9029

// 睡眠等待
function sleep(delay) {
    return new Promise((res) => setTimeout(res, delay * 1000));
}

// 获取 tDXRiskToken
// https://ly-sta.longhu.net/udid/c1
async function getDXRiskToken() {
    let ret = await axios.get('https://ly-sta.longhu.net/udid/c1', {
        headers: {
            'Param': 'j6JTUvz2q2JEGfoelONCGCmClO7wl3Jk-9l2lvYkG5BeVCrslOdEGCd5VflkqOV4Vp0mqOosV5owV5lkVOlkG3B4qOZHV9oe-vV6-CogVCr2PDJTUvRojh0EBCFePDJwr5I2q2gePDJ8GaBABk8nj5ETM9Zn-a4wBDmuo9mnM5otBZ-zYa0uo9mnM5o738V7V3lW-20TUvQEBZ8mG60co60Gya00rb0TlYdEGkQudDIfVOoXVa4e-aSFagmo3owTB9euUfo7RfYCUfIuBNlEr_-uMf4nV3GX-20-MfJuM9onV3YNV3z4BN-ml5N6UaIfVOzXVaBTB5HwBCF2YfEXVpB2PDJkMaBAqDw2GfV2q2J8M5QXMsdXB2w2U9V2qCZ6PDJClaBAVaw2Gfz2qCBgPDJ6lhV2q2Bp-pot-CGsB2w2GhB2q2Bp-pot-CGsB2w2d9I2q2ggqOSTB_06BCF6PDJTr6BAVaw2rsV2qCZTB5EXlDBAVaw2GvB2qCSTB5LkBCFwPDJgr6BABCStdbJ8l3Qgr_YEB2w2dfY2BCF2-pos-9z6VOGwGp7flvlC-poeG3SfqOYklOYmV3Jk-vV2PDJ_UaBABkdnMfdTla0JM5VXBDm03ozuqgNqRgeNBDm03ozTBZN-RD0aGvREMf4FYZguBZd6Gh0FUv-pBD7wjOSwVOSe-CV4ya0ZUhJEGszpROZeBblphpYWVD0wr8I8hpSTBZzpROZeyaBTB5mTG2BAl5NTrfoTB5mTM6BAdbJ8law2U9e6BCu5Gveplaw2U9eTBCu5Gveplaw2U9w2qCBTB_lpBCF2Vpr8qpGf-6BTB_dpBCF2Vpr8qpGf-6BTB5R2BCFwPDJpMaBAVDw2Gsz2qCG6-aw2Gh0wafYHBCF2-CVgVvoe-vowlOm2l9BfGC-kVC0m-3N5GCJ2-fVe-pZ2Wz==',
        }
    })
    return ret.data
}



// 1. 签到抽奖
{
    var data = '{"activity_no":' + activity_no + ',"task_id":""}';
    var luckyData = '{"activity_no":' + activity_no + ',"task_id":"","time":"2023-11-02 15:01:42","use_luck":0}';
    var configGetNumber = {
        method: 'post',
        url: 'https://longzhu.longfor.com/proxy/lmarketing-task-api-mvc-prod/openapi/task/v1/lottery/sign?__no_cache__=0.4360937475263842',
        headers: {
            'X-LF-UserToken': 'fc1f6572c6c0422ba93711cd3a92069a',
            'X-LF-Bu-Code': 'C40001',
            'X-LF-Channel': 'C4',
            'X-GAIA-API-KEY': 'c06753f1-3e68-437d-b592-b94656ea5517',
            'X-LF-DXRisk-Source': '5',
            'X-LF-DXRisk-Captcha-Token': '',
            'X-LF-DXRisk-Token': '65a09535iCEal3DcikQMRKKc63mDyuwLDdOsvDk1',
            'EagleEye-TraceID': 'ecc8e598f7f84cc38583365959a5f95e',
            'Cookie': `zg_did=%7B%22did%22%3A%20%2218cf61dc943c07-0d81c1eedad319-21f2132-1bcab9-18cf61dc9441072%22%7D; _dx_uzZo5y=869fb2bcac329bc352076999e091e2121589f7d144dc56797bac8b3f0c0ddceeea78528b; _dx_app_d1a43734fc59aeae9f1562dbd70fdf54=659f4305sWWiXA4n1qnOyZXe4ebBQlsN9Z0kLCh1; acw_tc=2760775317050227718963245ee876ccf61a9275a8a43d42d83d31f0eafb3f; SERVERID=f3c7e85ec13830172979a766a029921a|1705022821|1705022771; zg_d5bd8e6372844af9b43b8ce5bb74b787=%7B%22sid%22%3A%201705022772725%2C%22updated%22%3A%201705022821761%2C%22info%22%3A%201704936196424%2C%22superProperty%22%3A%20%22%7B%5C%22%E6%89%8B%E6%9C%BA%E5%8F%B7%5C%22%3A%20%5C%22185****4321%5C%22%2C%5C%22%E6%8C%87%E7%BA%B9%E8%AF%86%E5%88%AB%5C%22%3A%20%5C%2265a09535iCEal3DcikQMRKKc63mDyuwLDdOsvDk1%5C%22%2C%5C%22%E9%BE%99%E6%B0%91ID%5C%22%3A%20%5C%2235736066%5C%22%2C%5C%22%E7%94%A8%E6%88%B7%E7%B1%BB%E5%9E%8B%5C%22%3A%20%5C%22C1%5C%22%2C%5C%22%E6%B3%A8%E5%86%8C%E6%97%B6%E9%97%B4%5C%22%3A%20%5C%222021-04-04%2015%3A05%3A00%5C%22%2C%5C%22lmid%5C%22%3A%20%5C%2235736066%5C%22%2C%5C%22%E9%A1%B5%E9%9D%A2code%5C%22%3A%20%5C%2211111111111703831890354072540000%5C%22%2C%5C%22activity_no%5C%22%3A%20%5C%2211111111111703831890354072540000%5C%22%2C%5C%22channel%5C%22%3A%20%5C%22C4%5C%22%2C%5C%22buCode%5C%22%3A%20%5C%22C40001%5C%22%2C%5C%22ua%5C%22%3A%20%5C%22Mozilla%2F5.0%20(Windows%20NT%2010.0%3B%20Win64%3B%20x64)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F107.0.0.0%20Safari%2F537.36%20MicroMessenger%2F7.0.20.1781(0x6700143B)%20NetType%2FWIFI%20MiniProgramEnv%2FWindows%20WindowsWechat%2FWMPF%20XWEB%2F8447%5C%22%7D%22%2C%22platform%22%3A%20%22%7B%7D%22%2C%22utm%22%3A%20%22%7B%7D%22%2C%22referrerDomain%22%3A%20%22%22%2C%22landHref%22%3A%20%22https%3A%2F%2Flongzhu.longfor.com%2Flongball-homeh5%2F%23%2Factivity-lottery%2F%3FminiShare%3Dtrue%26activity_no%3D11111111111703831890354072540000%26source%3DApp%26webView%3Dmaia%26cityCode%3D100000%26sysType%3DWALLET%26businessSource%3D17%26entrance%3DLZ-MP-uapp-dl%26projectId%3D6230208720185679%26syncProjectId%3D617086213928861696%26pmsProjectId%3D617086213928861696%26sessionId%3Dfc1f6572c6c0422ba93711cd3a92069a%26token%3Dfc1f6572c6c0422ba93711cd3a92069a%26channel%3DC4%26buCode%3DC40001%26miniVersion%3D15.0.1%26appVersion%3D15.0.1%26constId%3D659f42e1KkDvSGsOHUNb05lhWUZhVV3q3eXMw7W4%26authorization%3Dfc1f6572c6c0422ba93711cd3a92069a%26lmToken%3Dfc1f6572c6c0422ba93711cd3a92069a%22%2C%22cuid%22%3A%20%2235736066%22%2C%22zs%22%3A%200%2C%22sc%22%3A%200%2C%22firstScreen%22%3A%201705022772725%7D`,
            'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
            'Content-Type': 'application/json;charset=UTF-8'
        },
        data: data
    };
    async function runLucky() {
        let tokens = LFUserToken.split('|')
        await getDXRiskToken()
        for (let idx = 0; idx < tokens.length; idx++) {
            let luckyResultMsg = ''
            let token = tokens[idx]
            configGetNumber.headers['X-LF-UserToken'] = token
            configGetNumber.headers['token'] = token
            configGetNumber.headers['X-LF-DXRisk-Token'] = await getDXRiskToken()
            // 获取抽奖次数
            let resRet = await axios(configGetNumber)
            console.log(JSON.stringify(resRet.data));
            // 开始抽奖
            configGetNumber.url = 'https://longzhu.longfor.com/proxy/lmarketing-task-api-mvc-prod/openapi/task/v1/lottery/luck?__no_cache__=0.3940563661238281'
            configGetNumber.data = luckyData
            let resRetResult = await axios(configGetNumber)
            console.log(JSON.stringify(resRetResult.data));

            let retData = resRetResult.data
            luckyResultMsg += `抽奖: ${idx + 1}号Token任务，${retData.desc || retData.message}`;
            console.log(luckyResultMsg);
            await send.sendNotify('龙湖抽奖', `${luckyResultMsg}`);
            await sleep(3)
        }
    }
    runLucky()
}

// 2. 每日签到
{

    var data = '{"activity_no":"11111111111686241863606037740000"}';

    var config = {
        method: 'post',
        url: 'https://longzhu.longfor.com/proxy/lmarketing-task-api-mvc-prod/openapi/task/v1/signature/clock',
        headers: {
            'X-LF-UserToken': 'c9c01002dc644309a68ba4fa15fd6e14',
            'X-LF-Bu-Code': 'C40001',
            'X-LF-Channel': 'C4',
            'X-GAIA-API-KEY': 'c06753f1-3e68-437d-b592-b94656ea5517',
            'X-LF-DXRisk-Source': '5',
            'X-LF-DXRisk-Captcha-Token': 'undefined',
            'X-LF-DXRisk-Token': '6541ab5f2a2fVe8k7TcOwTnxGCPz2Fu9VV6bEvJ1',
            'token': 'c9c01002dc644309a68ba4fa15fd6e14',
            'EagleEye-TraceID': '060d035a7c224dbf8b95e916728a559f',
            'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
            'Content-Type': 'application/json;charset=UTF-8'
        },
        data: data
    };



    async function run() {
        // 签到
        let tokens = LFUserToken.split('|')
        for (let idx = 0; idx < tokens.length; idx++) {
            let signResultMsg = ''
            let token = tokens[idx]
            config.headers['X-LF-UserToken'] = token
            config.headers['token'] = token
            let resRet = await axios(config)
            console.log(JSON.stringify(resRet.data));
            let retData = resRet.data
            if (retData.message == '' && retData.data.is_popup != 0) {
                signResultMsg += `签到: ${idx + 1}号Token任务，签到成功`;
            } else {
                signResultMsg += `签到: ${idx + 1}号Token任务，${retData.message || '签到失败'}`;
            }
            console.log(signResultMsg);
            await send.sendNotify('龙湖签到', `${signResultMsg}`);
            await sleep(3)
        }
    }

    run()
}

// 3. 每日话题赚龙珠

{
    let diffTime = dayjs(Date.now()).diff('2024-01-11', 'd')
    let TaskId = task_id + diffTime
    // 获取题目
    var config = {
        method: 'get',
        url: 'https://longzhu.longfor.com/proxy/lmarketing-task-api-prod/openapi/task/v1/information/list?task_id=' + TaskId,
        headers: {
            'Pragma': 'no-cache',
            'X-GAIA-API-KEY': 'caed5282-9019-418d-8854-3c34d02e0b4e',
            'X-LF-Bu-Code': 'C30403',
            'X-LF-Channel': 'C3',
            'X-LF-UserToken': GY_USERTOKEN,
            'Cookie': 'gr_user_id=8b5630b3-4efe-4311-a424-31b88ede5d4b; zg_did=%7B%22did%22%3A%20%2218cf2b97f661644-071aa549fbafbd-26001951-1fa400-18cf2b97f671ea7%22%7D; _dx_uzZo5y=ece1d1cb8cd80e2d4fbeedbb1277d7eb7f3fd83830a8572e02fd06da288190e15c265427; acw_tc=449ab1f7920a4b1e85e94180ae5dab0f765302f349f6138998f61cf54b431ded; _dx_app_d1a43734fc59aeae9f1562dbd70fdf54=659f4dcdI77jTwkhCeo8fPvot2SzhYlSavV1rul1; zg_d5bd8e6372844af9b43b8ce5bb74b787=%7B%22sid%22%3A%201704936928817%2C%22updated%22%3A%201704939439772%2C%22info%22%3A%201704879292266%2C%22superProperty%22%3A%20%22%7B%7D%22%2C%22platform%22%3A%20%22%7B%7D%22%2C%22utm%22%3A%20%22%7B%7D%22%2C%22referrerDomain%22%3A%20%22%22%2C%22landHref%22%3A%20%22https%3A%2F%2Flongzhu.longfor.com%2Flongball-homeh5%2F%23%2Factivity-lottery%2F%3FminiShare%3Dtrue%26activity_no%3D11111111111703831890354072540000%26source%3DApp%26webView%3Dmaia%26cityCode%3D100000%26sysType%3DWALLET%26businessSource%3D17%26entrance%3DLZ-MP-uapp-dl%26projectId%3D6230208720185679%26syncProjectId%3D617086213928861696%26pmsProjectId%3D617086213928861696%26sessionId%3Dfc1f6572c6c0422ba93711cd3a92069a%26token%3Dfc1f6572c6c0422ba93711cd3a92069a%26channel%3DC4%26buCode%3DC40001%26miniVersion%3D15.0.1%26appVersion%3D15.0.1%26constId%3D659e56757p9UQYtMnE0mQo8tzTL1I1XFXd5sLhA4%26authorization%3Dfc1f6572c6c0422ba93711cd3a92069a%26lmToken%3Dfc1f6572c6c0422ba93711cd3a92069a%22%2C%22cuid%22%3A%20%2235736066%22%2C%22zs%22%3A%200%2C%22sc%22%3A%200%2C%22firstScreen%22%3A%201704936928817%7D; SERVERID=e13b2eca6d4150ad68f7afa42be4d72b|1704939440|1704936926',
        }
    };
    // 答题
    var configTj = {
        method: 'post',
        url: 'https://longzhu.longfor.com/proxy/lmarketing-task-api-prod/openapi/task/v1/information/user',
        headers: {
            'EagleEye-TraceID': '6f3bce3d3fc9409896dfff70bc795bd1',
            'Pragma': 'no-cache',
            'X-GAIA-API-KEY': 'caed5282-9019-418d-8854-3c34d02e0b4e',
            'X-LF-Bu-Code': 'C30403',
            'X-LF-Channel': 'C3',
            'X-LF-DXRisk-Captcha-Token': '',
            'X-LF-DXRisk-Source': '3',
            'X-LF-DXRisk-Token': '659f4dcdI77jTwkhCeo8fPvot2SzhYlSavV1rul1',
            'X-LF-UserToken': GY_USERTOKEN,
            'Cookie': 'gr_user_id=8b5630b3-4efe-4311-a424-31b88ede5d4b; zg_did=%7B%22did%22%3A%20%2218cf2b97f661644-071aa549fbafbd-26001951-1fa400-18cf2b97f671ea7%22%7D; _dx_uzZo5y=ece1d1cb8cd80e2d4fbeedbb1277d7eb7f3fd83830a8572e02fd06da288190e15c265427; acw_tc=449ab1f7920a4b1e85e94180ae5dab0f765302f349f6138998f61cf54b431ded; _dx_app_d1a43734fc59aeae9f1562dbd70fdf54=659f4dcdI77jTwkhCeo8fPvot2SzhYlSavV1rul1; zg_d5bd8e6372844af9b43b8ce5bb74b787=%7B%22sid%22%3A%201704936928817%2C%22updated%22%3A%201704938957951%2C%22info%22%3A%201704879292266%2C%22superProperty%22%3A%20%22%7B%5C%22%E6%89%8B%E6%9C%BA%E5%8F%B7%5C%22%3A%20%5C%22185****4321%5C%22%2C%5C%22%E6%8C%87%E7%BA%B9%E8%AF%86%E5%88%AB%5C%22%3A%20%5C%22659f4dcdlcSqnmLohK3Hl55LSA4W8mRh8LkiZKF1%5C%22%2C%5C%22%E9%BE%99%E6%B0%91ID%5C%22%3A%20%5C%2235736066%5C%22%2C%5C%22%E7%94%A8%E6%88%B7%E7%B1%BB%E5%9E%8B%5C%22%3A%20%5C%22C1%5C%22%2C%5C%22%E6%B3%A8%E5%86%8C%E6%97%B6%E9%97%B4%5C%22%3A%20%5C%222021-04-04%2015%3A05%3A00%5C%22%2C%5C%22lmid%5C%22%3A%20%5C%2235736066%5C%22%2C%5C%22channel%5C%22%3A%20%5C%22C3%5C%22%2C%5C%22buCode%5C%22%3A%20%5C%22C30403%5C%22%2C%5C%22ua%5C%22%3A%20%5C%22Mozilla%2F5.0%20(iPhone%3B%20CPU%20iPhone%20OS%2016_6%20like%20Mac%20OS%20X)%20AppleWebKit%2F605.1.15%20(KHTML%2C%20like%20Gecko)%20Version%2F16.6%20Mobile%2F15E148%20Safari%2F604.1%5C%22%7D%22%2C%22platform%22%3A%20%22%7B%7D%22%2C%22utm%22%3A%20%22%7B%7D%22%2C%22referrerDomain%22%3A%20%22%22%2C%22landHref%22%3A%20%22https%3A%2F%2Flongzhu.longfor.com%2Flongball-homeh5%2F%23%2Factivity-lottery%2F%3FminiShare%3Dtrue%26activity_no%3D11111111111703831890354072540000%26source%3DApp%26webView%3Dmaia%26cityCode%3D100000%26sysType%3DWALLET%26businessSource%3D17%26entrance%3DLZ-MP-uapp-dl%26projectId%3D6230208720185679%26syncProjectId%3D617086213928861696%26pmsProjectId%3D617086213928861696%26sessionId%3Dfc1f6572c6c0422ba93711cd3a92069a%26token%3Dfc1f6572c6c0422ba93711cd3a92069a%26channel%3DC4%26buCode%3DC40001%26miniVersion%3D15.0.1%26appVersion%3D15.0.1%26constId%3D659e56757p9UQYtMnE0mQo8tzTL1I1XFXd5sLhA4%26authorization%3Dfc1f6572c6c0422ba93711cd3a92069a%26lmToken%3Dfc1f6572c6c0422ba93711cd3a92069a%22%2C%22cuid%22%3A%20%2235736066%22%2C%22zs%22%3A%200%2C%22sc%22%3A%200%2C%22firstScreen%22%3A%201704936928817%7D; SERVERID=e13b2eca6d4150ad68f7afa42be4d72b|1704939231|1704936926',
            'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
            'Content-Type': 'application/json;charset=UTF-8'
        },
        data: null
    };
    async function runHuaTiZhuan() {
        let gzhTaskdetailRet = await axios(config)
        if(!gzhTaskdetailRet.data.data){
            return
        }
        let {
            item_id,
            content
        } = gzhTaskdetailRet.data.data.information[0];
        console.log('[ item_id,content ] >', item_id, content)
        let user_answer = ''
        try {
            let pContent = JSON.parse(content)
            if (pContent.answer.length == 3) {
                user_answer = JSON.stringify({
                    user_answer: [1, 0, 0]
                })
            } else if (pContent.answer.length == 2) {
                user_answer = JSON.stringify({
                    user_answer: [1, 0]
                })
            } else {
                user_answer = JSON.stringify({
                    user_answer: [1, 0, 0, 0]
                })
            }
        } catch (error) {
            user_answer = JSON.stringify({
                user_answer: [1, 0, 0, 0]
            })
        }
        var data = {
            "token": GY_USERTOKEN,
            "channel": "C3",
            "bu_code": "C30403",
            "task_id": TaskId,
            "item_id": item_id,
            "item_content": user_answer
        }
        configTj.data = data
        // console.log('configTj', configTj);
        let huatiResult = await axios(configTj)
        // console.log('[ huatiResult ] >', huatiResult.data)
        await send.sendNotify('龙湖答题', `${huatiResult.data.message}`);
    }
    try {
        runHuaTiZhuan()
    } catch (error) {
        console.log(error);
    }

}