# 中奖数据解析工具

这个工具用于解析连续排列的姓名+手机号格式的中奖数据，并转换为Vue项目所需的JSON格式。

## 功能特点

- 🔍 **智能识别**：自动识别姓名和脱敏手机号的边界
- 📱 **脱敏支持**：直接处理包含****的脱敏手机号格式
- ⏰ **随机时间**：为每个中奖用户生成随机的中奖时间
- 🎯 **格式标准**：输出符合Vue项目要求的JSON格式
- ✅ **数据验证**：验证姓名和脱敏手机号格式的合理性

## 文件说明

- `parseWinnerData.js` - 主要解析函数
- `usage-example.js` - 使用示例和演示
- `README.md` - 使用说明文档

## 快速开始

### 1. 基本使用

```javascript
const { parseWinnerData } = require('./parseWinnerData');

// 您的脱敏格式数据（姓名+脱敏手机号连续排列）
const rawData = `张三138****5678李四139****4321王五150****8765`;

// 解析数据
const winners = parseWinnerData(rawData, '2', '民法典知识竞赛');

console.log(winners);
```

### 2. 输出格式

解析后的每个中奖用户对象包含以下字段：

```javascript
{
  "id": "2_1",                    // 唯一ID：活动ID_序号
  "name": "张三",                 // 用户姓名
  "phone": "138****5678",         // 脱敏手机号
  "activityId": "2",              // 活动ID
  "activityName": "民法典知识竞赛", // 活动名称
  "prizeAmount": 50,              // 奖金金额
  "winTime": "2024-11-28 16:45",  // 中奖时间
  "status": "issued"              // 发放状态
}
```

### 3. 运行示例

```bash
# 进入scripts目录
cd vue-legal-quiz/scripts

# 运行解析示例
node usage-example.js

# 或者直接运行主文件
node parseWinnerData.js
```

## 重要说明

### ⚠️ 数据格式要求

1. **脱敏手机号**：输入数据应为脱敏格式的手机号（如：138****5678）
2. **连续排列**：姓名和脱敏手机号必须连续排列，无分隔符
3. **有效姓名**：姓名应为2-8个中文字符

### 📝 您的数据处理

您提供的数据格式：
```
邹宝成137****3646邹海东152****1188邹蓉139****6683邹钰麟156****6247
```

**处理步骤：**

1. **直接使用脱敏数据**：无需修改，直接使用包含****的格式

2. **调用解析函数**：
   ```javascript
   const winners = parseWinnerData(yourData, '2', '民法典知识竞赛');
   ```

3. **生成JSON文件**：
   ```javascript
   const fs = require('fs');
   fs.writeFileSync('winners.json', JSON.stringify(winners, null, 2));
   ```

## 高级用法

### 批量处理多个活动

```javascript
const activities = [
  {
    id: '1',
    name: '宪法宣传周答题活动',
    data: '张三138****5678李四139****4321'
  },
  {
    id: '2',
    name: '民法典知识竞赛',
    data: '王五150****8765赵六186****5432'
  }
];

activities.forEach(activity => {
  const winners = parseWinnerData(activity.data, activity.id, activity.name);
  
  // 保存为JSON文件
  const filename = `winners-activity-${activity.id}.json`;
  fs.writeFileSync(filename, JSON.stringify(winners, null, 2));
});
```

### 自定义配置

```javascript
// 修改奖金金额
function parseWinnerDataCustom(rawData, activityId, activityName, prizeAmount = 50) {
  // ... 在parseWinnerData函数中修改prizeAmount值
}

// 自定义时间范围
function generateCustomWinTime(startDate, endDate) {
  // ... 自定义时间生成逻辑
}
```

## 注意事项

1. **数据隐私**：确保在处理真实手机号时遵守数据保护法规
2. **数据验证**：解析前请验证原始数据的完整性
3. **备份数据**：处理前请备份原始数据
4. **测试验证**：建议先用小量数据测试解析结果

## 故障排除

### 常见问题

1. **解析结果为空**
   - 检查手机号是否为正确的脱敏格式（如：138****5678）
   - 确认姓名为有效的中文字符

2. **姓名识别错误**
   - 检查数据中是否有特殊字符
   - 确认姓名长度在2-8个字符之间

3. **手机号格式错误**
   - 确保脱敏手机号格式正确（1开头3位数字+****+4位数字）
   - 检查****是否为4个星号

### 调试模式

在解析函数中添加调试信息：

```javascript
// 在extractNextWinner函数中添加
console.log('当前解析位置:', startIndex);
console.log('找到的姓名:', name);
console.log('找到的手机号:', phone);
```

## 联系支持

如果在使用过程中遇到问题，请检查：
1. 数据格式是否正确
2. Node.js版本是否兼容
3. 文件路径是否正确
