# 微信石头剪刀布Hook脚本 - Android轻量级版本

## 📋 脚本简介

这是一个专为Android微信设计的石头剪刀布Hook脚本，采用轻量级实现方式，最大程度减少对微信的性能影响，避免卡死问题。

**主要特性：**
- ✅ 支持用户交互式输入出拳序列
- ✅ 支持多种输入格式（点号、逗号、空格分隔）
- ✅ 循环序列控制，可设置复杂的出拳模式
- ✅ 轻量级Hook实现，避免微信卡死
- ✅ 完整的错误处理和输入验证
- ✅ 实时日志输出，便于调试

## 🚀 使用方法

### 1. 启动脚本
```bash
frida -U -f com.tencent.mm -l android-rps-simple.js --no-pause
```

### 2. 设置出拳序列
脚本启动后会自动弹出输入对话框，您可以输入想要的出拳序列：

**出拳类型对应：**
- `1` = 石头 ✊
- `2` = 剪刀 ✌️
- `3` = 布 ✋

**支持的输入格式：**
- **单个出拳**：`1` （固定出石头）
- **点号分隔**：`1.2.3` （石头→剪刀→布循环）
- **逗号分隔**：`1,2,3` （石头→剪刀→布循环）
- **空格分隔**：`1 2 3` （石头→剪刀→布循环）

**输入示例：**
- `1.2` - 石头和剪刀交替
- `3.3.3.1` - 三次布后一次石头
- `1,2,3,1,2,3` - 石头剪刀布循环两轮
- `2` - 固定出剪刀

### 3. 操作说明
- 点击"确定"：应用新的出拳序列
- 点击"取消"：保持当前序列不变
- 输入为空：保持当前序列不变

## 📊 工作原理

### Hook机制
脚本通过Hook `java.util.Random.nextInt(3)` 方法来控制出拳类型：
- 检测到参数为3的调用（石头剪刀布相关）
- 返回预设序列中的出拳类型（0-2对应1-3类型）
- 自动循环到序列下一个位置

### 序列循环
```
设置序列：[1, 2, 3] (石头, 剪刀, 布)
第1次出拳：石头 ✊
第2次出拳：剪刀 ✌️
第3次出拳：布 ✋
第4次出拳：石头 ✊（循环开始）
第5次出拳：剪刀 ✌️
...
```

## 🎯 策略示例

### 基础策略
- `1` - 永远出石头（对付总是出剪刀的对手）
- `2` - 永远出剪刀（对付总是出布的对手）
- `3` - 永远出布（对付总是出石头的对手）

### 进阶策略
- `1.2.3` - 标准循环，适合随机对手
- `1.1.2` - 偏向石头的策略
- `3.2.1` - 反向循环策略
- `1.3.2.1` - 复杂混合策略

### 心理战术
- `1.1.1.2` - 连续石头后突然剪刀
- `2.3.1.2.3.1` - 复杂不规律序列

## 🔧 技术特性

### 轻量级设计
- 最小化Hook范围，只处理石头剪刀布相关调用
- 减少日志输出频率（每10次才输出一次）
- 快速检查机制，避免不必要的处理

### 错误处理
- 输入验证：只接受1-3范围内的出拳类型
- 环境检查：验证Java环境和dialog对象可用性
- 异常捕获：完整的try-catch错误处理

### 兼容性
- 保留原有的内部函数接口
- 支持多种输入分隔符
- 向后兼容旧版本的核心逻辑

## 📝 日志说明

脚本运行时会输出详细的日志信息：

```
[时间] 📱 Android微信石头剪刀布Hook启动 (轻量级版本)
[时间] ✅ Java环境正常
[时间] 🚀 开始Hook Random.nextInt...
[时间] ✅ Random Hook成功
[时间] 🎉 初始化完成！
[时间] 🎮 弹出出拳设置对话框...
[时间] ✅ 设置出拳序列: [石头, 剪刀, 布]
[时间] 🎯 用户设置序列成功: [石头, 剪刀, 布]
[时间] ✊ 出拳控制: 石头 (序列第1次调用)
```

## ⚠️ 注意事项

1. **仅供学习研究使用**，请勿用于任何违法违规活动
2. **环境要求**：需要root权限的Android设备和Frida环境
3. **兼容性**：主要针对Android版微信，其他版本可能需要调整
4. **性能影响**：已优化为轻量级实现，但仍可能对微信性能有轻微影响
5. **风险提示**：使用Hook工具存在被检测风险，请谨慎使用
6. **游戏公平性**：请在朋友间娱乐时告知使用脚本，保持游戏公平

## 🎮 游戏技巧

### 了解对手
- 观察对手的出拳习惯
- 记录对手的常用策略
- 根据对手调整自己的序列

### 序列设计
- 避免过于规律的序列
- 适当加入随机元素
- 根据实战效果调整策略

## 🔄 版本信息

### v1.0 (当前版本)
- ✅ 基础石头剪刀布Hook功能
- ✅ dialog.input交互式输入
- ✅ 多种输入格式支持
- ✅ 循环序列控制
- ✅ 轻量级Hook实现
- ✅ 完整错误处理

## 📞 技术支持

如遇到问题，请检查：
1. Frida环境是否正确安装
2. 设备是否已获得root权限
3. 微信版本是否兼容
4. 输入的出拳序列是否符合格式要求（1-3范围）
5. 是否在石头剪刀布游戏中使用

---

**免责声明**：本脚本仅供技术学习和研究使用，使用者需自行承担使用风险。请在朋友间娱乐使用时保持游戏公平性。
