# 邮件自动发送脚本

这是一个基于 Node.js 和 nodemailer 的自动邮件发送脚本，支持发送格式化的HTML邮件。

## 功能特性

- ✅ 支持163邮箱SMTP发送
- ✅ 环境变量配置管理
- ✅ 美观的HTML邮件模板
- ✅ 完整的错误处理
- ✅ 发送状态反馈
- ✅ 中文本地化支持

## 安装依赖

```bash
# 使用 pnpm (推荐)
pnpm add nodemailer

# 或使用 npm
npm install nodemailer
```

## 配置说明

### 1. 创建环境变量文件

复制 `.env.example` 文件为 `.env`：

```bash
cp .env.example .env
```

### 2. 配置163邮箱授权码

1. 登录163邮箱网页版
2. 进入 **设置** → **POP3/SMTP/IMAP**
3. 开启 **SMTP服务**
4. 点击 **生成授权码**
5. 将生成的授权码填入 `.env` 文件的 `SENDER_PASSWORD` 字段

### 3. 编辑配置文件

编辑 `.env` 文件，填入正确的配置信息：

```env
SENDER_EMAIL=<EMAIL>
SENDER_PASSWORD=your_auth_code_here
SENDER_NAME=Your Name
RECIPIENT_EMAIL=<EMAIL>
RECIPIENT_NAME=Recipient Name
```

## 使用方法

### 直接运行

```bash
node mail/mail.js
```

### 使用环境变量

```bash
# 临时设置环境变量
SENDER_PASSWORD=your_auth_code node mail/mail.js

# 或者使用 dotenv
npm install dotenv
```

### 作为模块使用

```javascript
const EmailSender = require('./mail/mail.js');

async function sendTestEmail() {
    const emailSender = new EmailSender();
    const result = await emailSender.sendEmail();
    
    if (result.success) {
        console.log('邮件发送成功！');
    } else {
        console.error('邮件发送失败:', result.error);
    }
    
    emailSender.close();
}

sendTestEmail();
```

## 邮件内容

脚本会自动生成包含以下信息的邮件：

- 📅 **时间信息**: 发送时间、星期
- 👤 **发送者信息**: 发送者姓名、邮箱
- 📊 **系统状态**: 邮件系统状态
- 📝 **邮件目的**: 说明邮件用途

## 自定义邮件内容

如需自定义邮件内容，可以修改 `generateEmailContent()` 方法：

```javascript
generateEmailContent() {
    // 自定义邮件主题
    const subject = '您的自定义主题';
    
    // 自定义HTML内容
    const htmlContent = `
        <h1>自定义邮件内容</h1>
        <p>这里是您的自定义内容</p>
    `;
    
    return { subject, html: htmlContent, text: '纯文本内容' };
}
```

## 错误处理

脚本包含完整的错误处理机制：

- SMTP连接验证
- 授权码检查
- 发送状态监控
- 详细错误信息输出

## 安全注意事项

1. **不要将授权码硬编码在脚本中**
2. **将 `.env` 文件添加到 `.gitignore`**
3. **定期更换邮箱授权码**
4. **不要在公共场所展示授权码**

## 常见问题

### Q: 提示"请设置发送者邮箱授权码"
A: 请确保已正确设置 `SENDER_PASSWORD` 环境变量或 `.env` 文件。

### Q: SMTP连接验证失败
A: 请检查：
- 163邮箱是否开启了SMTP服务
- 授权码是否正确
- 网络连接是否正常

### Q: 邮件发送成功但收不到
A: 请检查：
- 接收邮箱的垃圾邮件文件夹
- 接收邮箱地址是否正确
- 邮件服务商的反垃圾邮件策略

## 许可证

MIT License
