<template>
  <div class="min-h-screen">
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="flex items-center justify-between p-4">
        <i class="fas fa-arrow-left text-white text-lg cursor-pointer" @click="goBack"></i>
        <h1 class="text-white text-lg font-semibold">活动详情</h1>
        <i class="fas fa-share-alt text-white text-lg cursor-pointer hover:text-yellow-300 transition-colors duration-200" @click="shareCurrentPage"></i>
      </div>
    </div>

    <template v-if="activity">
      <!-- 活动信息 -->
      <div class="activity-header">
        <div class="flex items-center mb-4">
          <div class="activity-icon mr-4">
            <i :class="activity.icon" class="text-white text-2xl"></i>
          </div>
          <div>
            <h2 class="text-xl font-bold text-gray-800">{{ activity.name }}</h2>
            <p class="text-gray-600 text-sm">{{ activity.description }}</p>
            <div class="mt-2">
              <span class="status-badge" :class="getStatusClass(activity.status)">
                <i :class="getStatusIcon(activity.status)" class="mr-1"></i>
                {{ getStatusText(activity.status) }}
              </span>
            </div>
          </div>
        </div>
        <div class="text-sm text-gray-600">
          <p class="flex items-center mb-1">
            <i class="fas fa-calendar mr-2 text-red-500"></i>
            活动时间：{{ activity.startDate }} 至 {{ activity.endDate }}
          </p>
          <p class="flex items-center">
            <i class="fas fa-gift mr-2 text-yellow-500"></i>
            奖品：50元话费（每人）
          </p>
        </div>
      </div>

      <!-- 搜索栏 (只有已公布结果的活动才显示) -->
      <div class="search-bar" v-if="activity.resultStatus === 'published'">
        <i class="fas fa-search text-gray-400 mr-3"></i>
        <input
          v-model="searchKeyword"
          type="text"
          class="search-input"
          placeholder="搜索中奖用户姓名或手机号"
          @keypress.enter="handleSearch"
        >
        <i
          class="fas fa-times text-gray-400 ml-3 cursor-pointer"
          @click="clearSearch"
        ></i>
      </div>

      <!-- 统计信息 -->
      <div class="stats-container">
        <div>
          <div class="text-2xl font-bold text-red-600">
            {{  activity.winnerCount || '待公布' }}
          </div>
          <div class="text-xs text-gray-600">中奖人数</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-yellow-600">
            {{ formatAmount(activity.totalPrize) || '待公布' }}
          </div>
          <div class="text-xs text-gray-600">总奖金</div>
        </div>
        <div>
          <div class="text-2xl font-bold" :class="getResultStatusColor(activity.resultStatus)">
            {{ getResultStatusText(activity.resultStatus) }}
          </div>
          <div class="text-xs text-gray-600">中奖结果</div>
        </div>
      </div>

      <!-- 中奖名单 -->
      <div class="flex-1 pb-6">
        <!-- 未公布结果的提示 -->
        <div v-if="activity.resultStatus === 'unpublished'" class="text-center py-16 text-white">
          <div class="empty-icon mx-auto mb-6">
            <i class="fas fa-clock text-white text-4xl opacity-60"></i>
          </div>
          <h3 class="text-xl font-bold mb-4">中奖结果尚未公布</h3>
          <p class="text-sm opacity-80 mb-2">
            {{ activity.status === 'active' ? '活动正在进行中' : '活动已结束' }}
          </p>
          <p class="text-xs opacity-60">
            {{ activity.status === 'active' ? '请等待活动结束后查看中奖结果' : '中奖结果将在近期公布，请耐心等待' }}
          </p>
        </div>

        <!-- 已公布结果的中奖名单 -->
        <template v-else>
          <LoadingSpinner v-if="activityStore.loading" />

          <template v-else>
            <div
              v-for="winner in displayedWinners"
              :key="winner.id"
              class="winner-card cursor-pointer hover:shadow-lg transition-all duration-300"
              @click="showWinnerDetail(winner)"
            >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="winner-icon mr-3">
                  <i class="fas fa-user text-white text-lg"></i>
                </div>
                <div>
                  <h3 class="font-bold text-gray-800">{{ winner.name }}</h3>
                  <p class="text-gray-600 text-sm">{{ winner.phone }}</p>
                </div>
              </div>
              <div class="text-right">
                <div class="prize-badge">
                  <i class="fas fa-gift mr-1"></i>
                  50元话费
                </div>
                <p class="text-xs text-gray-500 mt-1">{{ winner.winTime }}</p>
              </div>
            </div>
          </div>
          </template>
          <!-- 加载更多按钮 -->
          <div class="text-center py-6" v-if="hasMoreWinners">
            <button class="load-more-btn" @click="loadMoreWinners">
              <i class="fas fa-chevron-down mr-2"></i>
              加载更多中奖用户
            </button>
          </div>
        </template>
      </div>
    </template>

    <!-- 活动不存在 -->
    <div v-else class="flex items-center justify-center h-64">
      <div class="text-center text-white">
        <i class="fas fa-exclamation-triangle text-4xl mb-4 opacity-60"></i>
        <p class="text-lg">活动不存在</p>
      </div>
    </div>

    <!-- 中奖详情弹窗 -->
    <div
      class="modal-overlay"
      :class="{ show: showWinnerModal }"
      @click.self="closeWinnerModal"
    >
      <div class="modal-content" v-if="selectedWinner">
        <!-- 弹窗头部 -->
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-bold text-gray-800">中奖详情</h3>
          <i
            class="fas fa-times text-gray-400 text-xl cursor-pointer hover:text-gray-600 transition-colors duration-200"
            @click="closeWinnerModal"
          ></i>
        </div>

        <!-- 用户信息 -->
        <div class="text-center mb-6">
          <div class="winner-detail-icon mx-auto mb-4">
            <i class="fas fa-user text-white text-3xl"></i>
          </div>
          <h4 class="text-xl font-bold text-gray-800 mb-2">{{ selectedWinner.name }}</h4>
          <p class="text-gray-600">{{ selectedWinner.phone }}</p>
        </div>

        <!-- 中奖信息 -->
        <div class="space-y-4 mb-6">
          <div class="flex items-center justify-between py-2 border-b border-gray-100">
            <span class="text-gray-600">中奖活动</span>
            <span class="font-semibold text-gray-800">{{ selectedWinner.activityName }}</span>
          </div>
          <div class="flex items-start justify-between py-2 border-b border-gray-100">
            <span class="text-gray-600">活动描述</span>
            <span class="font-semibold text-gray-800 text-right max-w-48">{{ getActivityDescription(selectedWinner.activityId) }}</span>
          </div>
          <div class="flex items-center justify-between py-2 border-b border-gray-100">
            <span class="text-gray-600">奖品</span>
            <span class="font-semibold text-yellow-600">
              <i class="fas fa-gift mr-1"></i>
              {{ selectedWinner.prizeAmount }}元话费
            </span>
          </div>
          <div class="flex items-center justify-between py-2">
            <span class="text-gray-600">中奖时间</span>
            <span class="font-semibold text-gray-800">{{ selectedWinner.winTime }}</span>
          </div>
        </div>

        <!-- 关闭按钮 -->
        <button
          class="w-full bg-gradient-to-r from-red-500 to-yellow-500 text-white py-3 rounded-xl font-semibold transition-all duration-300 hover:shadow-lg"
          @click="closeWinnerModal"
        >
          知道了
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useActivityStore } from '@/stores/activity'
import LoadingSpinner from '@/components/LoadingSpinner.vue'
import type { Activity, Winner, ActivityStatus } from '@/types'
import { formatAmount } from '@/utils/format'

const route = useRoute()
const router = useRouter()
const activityStore = useActivityStore()

const searchKeyword = ref('')
const winners = ref<Winner[]>([])
const allWinners = ref<Winner[]>([]) // 存储所有中奖用户
const displayCount = ref(5) // 当前显示的用户数量
const pageSize = 5 // 每次加载的用户数量

// 弹窗相关状态
const showWinnerModal = ref(false)
const selectedWinner = ref<Winner | null>(null)

// 获取当前活动
const activity = computed((): Activity | undefined => {
  const activityId = route.params.id as string
  return activityStore.getActivityById(activityId)
})

// 当前显示的中奖用户列表
const displayedWinners = computed(() => {
  return allWinners.value.slice(0, displayCount.value)
})

// 是否还有更多用户可以加载
const hasMoreWinners = computed(() => {
  return displayCount.value < allWinners.value.length
})

// 返回上一页
const goBack = () => {
  router.back()
}

// 处理搜索
const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push({
      name: 'Search',
      query: { 
        keyword: searchKeyword.value.trim(),
        activityId: route.params.id as string
      }
    })
  }
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
}

// 格式化时间
const formatTime = (timeStr: string) => {
  return timeStr.split(' ')[1] // 只显示时间部分
}

// 获取状态相关方法（复用）
const getStatusClass = (status: ActivityStatus) => {
  return status === 'active' 
    ? 'bg-green-100 text-green-800' 
    : 'bg-gray-100 text-gray-600'
}

const getStatusIcon = (status: ActivityStatus) => {
  return status === 'active' ? 'fas fa-play' : 'fas fa-check'
}

const getStatusText = (status: ActivityStatus) => {
  return status === 'active' ? '进行中' : '已结束'
}

const getStatusColor = (status: ActivityStatus) => {
  return status === 'active' ? 'text-green-600' : 'text-gray-600'
}

// 获取中奖结果公布状态文本
const getResultStatusText = (resultStatus: string) => {
  return resultStatus === 'published' ? '已公布' : '未公布'
}

// 获取中奖结果公布状态颜色
const getResultStatusColor = (resultStatus: string) => {
  return resultStatus === 'published' ? 'text-green-600' : 'text-orange-600'
}

// 加载中奖名单
const loadWinners = async () => {
  if (activity.value) {
    const winnersData = await activityStore.getWinnersByActivity(activity.value.id)
    allWinners.value = winnersData
    winners.value = winnersData
    // 重置显示数量
    displayCount.value = Math.min(pageSize, winnersData.length)
  }
}

// 加载更多中奖用户
const loadMoreWinners = () => {
  const newCount = displayCount.value + pageSize
  displayCount.value = Math.min(newCount, allWinners.value.length)
}

// 显示中奖用户详情
const showWinnerDetail = (winner: Winner) => {
  selectedWinner.value = winner
  showWinnerModal.value = true
}

// 关闭中奖用户详情弹窗
const closeWinnerModal = () => {
  showWinnerModal.value = false
  selectedWinner.value = null
}

// 获取活动描述
const getActivityDescription = (activityId: string) => {
  const targetActivity = activityStore.getActivityById(activityId)
  return targetActivity?.description || '暂无描述'
}

// 分享当前页面
const shareCurrentPage = async () => {
  try {
    const currentUrl = window.location.href

    // 尝试使用现代浏览器的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(currentUrl)
      showToast('链接已复制到剪贴板')
    } else {
      // 降级方案：使用传统的复制方法
      const textArea = document.createElement('textarea')
      textArea.value = currentUrl
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        document.execCommand('copy')
        showToast('链接已复制到剪贴板')
      } catch (err) {
        console.error('复制失败:', err)
        showToast('复制失败，请手动复制链接')
      } finally {
        document.body.removeChild(textArea)
      }
    }
  } catch (err) {
    console.error('分享失败:', err)
    showToast('分享失败，请重试')
  }
}

// 显示提示信息
const showToast = (message: string) => {
  // 创建提示元素
  const toast = document.createElement('div')
  toast.textContent = message
  toast.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 text-white px-4 py-2 rounded-lg text-sm z-50 transition-all duration-300'

  document.body.appendChild(toast)

  // 显示动画
  setTimeout(() => {
    toast.style.opacity = '1'
    toast.style.transform = 'translate(-50%, 0)'
  }, 10)

  // 3秒后移除
  setTimeout(() => {
    toast.style.opacity = '0'
    toast.style.transform = 'translate(-50%, -20px)'
    setTimeout(() => {
      if (document.body.contains(toast)) {
        document.body.removeChild(toast)
      }
    }, 300)
  }, 3000)
}

onMounted(() => {
  loadWinners()
})
</script>

<style scoped>
.activity-header {
  @apply bg-white bg-opacity-90 backdrop-blur-lg mx-5 my-4 p-5 rounded-2xl border border-white border-opacity-20;
}

.activity-icon {
  @apply w-16 h-16 bg-gradient-to-r from-red-500 to-yellow-500 rounded-full flex items-center justify-center;
}

.status-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.winner-icon {
  @apply w-10 h-10 bg-gradient-to-r from-legal-gold-400 to-legal-gold-500 rounded-lg flex items-center justify-center;
}

.prize-badge {
  @apply bg-gradient-to-r from-legal-gold-400 to-legal-gold-500 text-white px-3 py-1 rounded-full text-xs font-semibold;
}

.load-more-btn {
  @apply bg-white bg-opacity-20 backdrop-blur-lg text-white px-6 py-3 rounded-full border border-white border-opacity-30 transition-all duration-300 hover:bg-opacity-30;
}

.empty-icon {
  @apply w-32 h-32 bg-white bg-opacity-10 rounded-full flex items-center justify-center backdrop-blur-lg border-2 border-white border-opacity-20;
}

.winner-detail-icon {
  @apply w-20 h-20 bg-gradient-to-r from-legal-gold-400 to-legal-gold-500 rounded-full flex items-center justify-center shadow-lg;
}
</style>
