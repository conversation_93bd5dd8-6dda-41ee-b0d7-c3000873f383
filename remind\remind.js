const axios = require("axios");
const send = require("../sendNotify.js")
// https://www.jisilu.cn/data/cbnew/cb_list_new/?___jsl=LST___t=1634804314662

// 集思录
let JSLCOOKIE = process.env.JSL_COOKIE || 'kbzw__Session=7ek12626pfqd3q19h8ktkdt6n3; kbz_newcookie=1; kbzw__user_login=7Obd08_P1ebax9aX7sffxOnY1uPG4oKvpuXK7N_u0ejF1dSeq5PWk6qvqpyppayZr5ao26qrw6aW2KmrotqjqcXXldyYrqXW2cXS1qCarpynlauTmLKgzaLOvp_G5OPi2OPDpZalp5OgrtHE3efp2OzUsoK_z-vc35iu0cSUyMiJ0YyoktWSrNaqoYGx0eTl2sDezsLL6pCsqqqmlKaBnMS9vca4o4LiyuLck7_G08zjopWs4d7mz9uQsKuhqI-kl6Svo6CqjMrD3MLp4KKtlKePp68.; Hm_lvt_164fe01b1433a19b507595a43bf58262=1634706933,1634708992,1634709045,1634710153; Hm_lpvt_164fe01b1433a19b507595a43bf58262=1634804066'


function start() {
    xinzhai()
}

// 新债提醒打新及上市
async function xinzhai() {
    let {
        nowDay,
        nowMonth,
        nowFullYear,
    } = initDate()
    let xinZhaiNewAdd = []
    let xinZhaiUpAdd = []
    let xinZhaiMtUpAdd = []
    // 获取数据
    let ret = await axios.post('https://www.jisilu.cn/data/cbnew/cb_list_new/?___jsl=LST___t=1634804314662', {}, {
        headers: {
            'Cookie': JSLCOOKIE
        }
    })
    let xinzhaiRows = ret.data.rows
    for (let i = 0; i < xinzhaiRows.length; i++) {
        let item = xinzhaiRows[i]
        let short_maturity_dt = item.cell.short_maturity_dt;
        let list_dt = item.cell.list_dt;
        let splitDt = !short_maturity_dt ? [] : short_maturity_dt.split('-')
        let splitSSDt = !list_dt ? [] : list_dt.split('-')
        // 判断今天是否有一个新债
        if (!list_dt && splitDt.length == 3 && splitDt[1] == nowMonth && splitDt[2] == nowDay) {
            // 今天有新债
            xinZhaiNewAdd.push(item.cell.bond_nm)
        }

        // 判断今天是否有新债上市
        if (splitSSDt.length == 3 && splitSSDt[0] == nowFullYear && splitSSDt[1] == nowMonth && splitSSDt[2] == nowDay) {
            xinZhaiUpAdd.push(item.cell.bond_nm)
        }

        // 判断明天是否有新债上市
        if (splitSSDt.length == 3 && splitSSDt[0] == nowFullYear && splitSSDt[1] == nowMonth && splitSSDt[2] == nowDay + 1) {
            xinZhaiMtUpAdd.push(item.cell.bond_nm)
        }
    }
    console.log(xinZhaiNewAdd, xinZhaiUpAdd, xinZhaiMtUpAdd)
    if (xinZhaiNewAdd.length > 0 || xinZhaiUpAdd.length > 0 || xinZhaiMtUpAdd.length > 0) {
        await send.sendNotify('新债提醒', `新债提醒：\n有新的新债${xinZhaiNewAdd.length},${xinZhaiNewAdd.join(',')}\n新债上市${xinZhaiUpAdd.length},${xinZhaiUpAdd.join(',')}\n明天新债上市${xinZhaiMtUpAdd.length},${xinZhaiMtUpAdd.join(',')}`, 'taojin')
    }
}


// 初始化日期
function initDate() {
    let nowdate = new Date();
    let nowDay = nowdate.getDate();
    let nowMonth = nowdate.getMonth();
    let nowDayOfWeek = nowdate.getDay();
    let nowFullYear = nowdate.getFullYear();
    return {
        nowDay,
        nowMonth: nowMonth + 1,
        nowFullYear,
        nowDayOfWeek
    }
}

start()