/**
 * 解析中奖用户数据
 * 输入格式：姓名+手机号连续排列的字符串
 * 输出格式：中奖用户对象数组
 */

/**
 * 解析中奖用户数据的主函数
 * @param {string} rawData - 原始数据字符串
 * @param {string} activityId - 活动ID
 * @param {string} activityName - 活动名称
 * @param {string} winTime - 中奖时间
 * @returns {Array} 解析后的中奖用户数组
 */
function parseWinnerData(rawData, activityId, activityName,winTime='') {
  // 清理数据，移除多余的空白字符
  const cleanData = rawData.replace(/\s+/g, '');
  
  const winners = [];
  let currentIndex = 0;
  let winnerId = 1;
  
  while (currentIndex < cleanData.length) {
    const result = extractNextWinner(cleanData, currentIndex);
    
    if (result) {
      const { name, phone, nextIndex } = result;
      
      // 创建中奖用户对象
      const winner = {
        id: `${activityId}_${winnerId}`,
        name: name,
        phone: formatPhone(phone),
        activityId: activityId,
        activityName: activityName,
        prizeAmount: 50,
        winTime: winTime,
        status: 'issued'
      };
      
      winners.push(winner);
      currentIndex = nextIndex;
      winnerId++;
    } else {
      // 如果无法解析，跳过一个字符
      currentIndex++;
    }
  }
  
  return winners;
}

/**
 * 从指定位置提取下一个中奖用户信息
 * @param {string} data - 数据字符串
 * @param {number} startIndex - 开始位置
 * @returns {Object|null} 提取结果或null
 */
function extractNextWinner(data, startIndex) {
  // 脱敏手机号正则：1开头3位数字 + **** + 4位数字
  const maskedPhoneRegex = /1[3-9]\d\*\*\*\*\d{4}/;

  // 从当前位置开始查找脱敏手机号
  for (let i = startIndex; i < data.length - 10; i++) {
    const substring = data.substring(i);
    const phoneMatch = substring.match(maskedPhoneRegex);

    if (phoneMatch && phoneMatch.index === 0) {
      // 找到脱敏手机号，提取姓名
      const phone = phoneMatch[0];
      const name = data.substring(startIndex, i);

      // 验证姓名是否合理（2-8个中文字符）
      if (isValidName(name)) {
        return {
          name: name,
          phone: phone,
          nextIndex: i + phone.length // 脱敏手机号长度为11
        };
      }
    }
  }

  return null;
}

/**
 * 验证姓名是否合理
 * @param {string} name - 姓名
 * @returns {boolean} 是否为有效姓名
 */
function isValidName(name) {
  // 姓名应该是2-4个中文字符，可能包含少数民族姓名中的·
  const nameRegex = /^[\u4e00-\u9fa5·]{2,8}$/;
  return nameRegex.test(name) && name.length >= 2 && name.length <= 8;
}

/**
 * 格式化手机号（输入已经是脱敏格式，直接返回）
 * @param {string} phone - 脱敏手机号
 * @returns {string} 格式化后的手机号
 */
function formatPhone(phone) {
  // 输入已经是脱敏格式，直接返回
  return phone;
}

/**
 * 生成随机中奖时间
 * @returns {string} 格式化的中奖时间
 */
function generateRandomWinTime() {
  const now = new Date();
  const startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30天前
  const randomTime = new Date(startDate.getTime() + Math.random() * (now.getTime() - startDate.getTime()));
  
  const year = randomTime.getFullYear();
  const month = String(randomTime.getMonth() + 1).padStart(2, '0');
  const day = String(randomTime.getDate()).padStart(2, '0');
  const hour = String(randomTime.getHours()).padStart(2, '0');
  const minute = String(randomTime.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hour}:${minute}`;
}

/**
 * 使用示例
 */
function example() {
  const rawData = `邹宝成137****3646邹海东152****1188邹蓉139****6683邹钰麟156****6247`;

  // 直接解析脱敏格式的数据
  const winners = parseWinnerData(rawData, '2', '民法典知识竞赛');

  console.log('解析结果：');
  console.log(JSON.stringify(winners, null, 2));
  console.log(`总共解析出 ${winners.length} 个中奖用户`);

  return winners;
}



// 导出函数
module.exports = {
  parseWinnerData,
  formatPhone,
  generateRandomWinTime,
  example
};

// 如果直接运行此文件，执行示例
if (require.main === module) {
  example();
}
