// 纪念币准确预约提醒程序
// http://www.pbc.gov.cn/huobijinyinju/147948/2991918/index.html

import axios from "axios"
import { load } from "cheerio"
import dayjs from "dayjs"
// import send from "../sendNotify.js"

// 中国银行公告地址
const ZGYH_NOTICE = "http://www.pbc.gov.cn/huobijinyinju/147948/2991918/index.html"
// 获取5天前时间
const sevenAgoTime = dayjs().subtract(2, 'd')

async function start() {
    let ret = await axios.get(ZGYH_NOTICE+'?'+Date.now())
    const $ = load(ret.data)
    // 找到第一个公告连接
    for (let i = 0; i < 2; i++) {
        // 获取元素
        let ele = $('.newslist_style').eq(i)
        // 获取公告链接
        let url = ele.find('a').attr('href') as string;
        // 获取公告标题
        let atext = ele.find('a').text() as string;
        // 获取公告时间
        let date = ele.parent().find('hui12').text() as string;
        // console.log(url);
        let isNJBI = true
        // let isNJBI = await getNoticeContent(url)
        let formatDate = date.replace('年','-').replace('月','-').replace('日','')
        console.log('[ formatDate ] >', formatDate)
        if (isNJBI) {
            console.log('[ 发现纪念币 ] >\n', `公告名称：${atext.trim()} \n 公告链接：${url} \n 日期: ${date}`)
            if (dayjs(formatDate).isAfter(sevenAgoTime)) {
                // 发布的公告是否7天内发布
                console.log('[ 7天内发布 ] >')
                // await send.sendNotify('发现纪念币', `公告名称：${atext.trim()} \n 公告链接：${url} \n 日期: ${date}`);
            }else{
                console.log('[ 公告发布超过7天 ] >')
            }
        }
    }
}

async function getNoticeContent(url: string) {
    let ret = await axios.get(url)
    const $ = load(ret.data)
    /* let plength = $('#zoom p').length
    for (let i = 0; i < plength; i++) {
        let ptext = $('#zoom p').eq(i).text();
        if (ptext.indexOf('纪念币') > -1) {
            // 发现纪念币
            return true
        }
    } */
    let zoomhtml = $('#zoom').html() as string
    if (zoomhtml.indexOf('纪念币') > -1) {
        // 发现纪念币
        return true
    }
    return false
}
start()