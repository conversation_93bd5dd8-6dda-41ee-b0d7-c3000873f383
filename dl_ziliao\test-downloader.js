#!/usr/bin/env node

const BatchDownloader = require('./batch-downloader');
const fs = require('fs').promises;
const path = require('path');

/**
 * 测试批量下载器
 */
async function testDownloader() {
    console.log('🧪 开始测试批量下载器...\n');

    try {
        // 检查 JSON 文件是否存在
        const jsonPath = path.join(__dirname, 'ziliao.json');
        console.log('📋 检查 JSON 文件...');
        
        try {
            await fs.access(jsonPath);
            console.log('✅ JSON 文件存在');
        } catch {
            console.error('❌ JSON 文件不存在:', path.basename(jsonPath));
            return;
        }

        // 读取并验证 JSON 文件格式
        console.log('🔍 验证 JSON 文件格式...');
        const data = await fs.readFile(jsonPath, 'utf8');
        const jsonData = JSON.parse(data);
        
        if (!jsonData.entity || !jsonData.entity.planLibList) {
            console.error('❌ JSON 文件格式不正确');
            return;
        }
        
        const fileCount = jsonData.entity.planLibList.length;
        console.log(`✅ JSON 格式正确，找到 ${fileCount} 个文件`);

        // 显示前几个文件信息
        console.log('\n📄 文件列表预览:');
        jsonData.entity.planLibList.slice(0, 3).forEach((file, index) => {
            console.log(`${index + 1}. ${file.libName}`);
            console.log(`   URL: ${file.libUrl}`);
        });
        
        if (fileCount > 3) {
            console.log(`   ... 还有 ${fileCount - 3} 个文件`);
        }

        // 创建下载器实例（测试模式：只下载前2个文件）
        console.log('\n🚀 创建下载器实例...');
        const downloader = new BatchDownloader({
            downloadDir: './test-downloads',
            maxConcurrent: 2,
            retryAttempts: 2,
            retryDelay: 500
        });

        // 创建测试用的小型 JSON 文件
        const testJsonPath = path.join(__dirname, 'test-ziliao.json');
        const testData = {
            entity: {
                planLibList: jsonData.entity.planLibList.slice(0, 2) // 只取前2个文件进行测试
            }
        };
        
        await fs.writeFile(testJsonPath, JSON.stringify(testData, null, 2));
        console.log(`✅ 创建测试文件: ${path.basename(testJsonPath)} (包含 ${testData.entity.planLibList.length} 个文件)`);

        // 开始测试下载
        console.log('\n📥 开始测试下载...');
        await downloader.startDownload(testJsonPath);

        // 清理测试文件
        console.log('\n🧹 清理测试文件...');
        try {
            await fs.unlink(testJsonPath);
            console.log('✅ 测试文件已清理');
        } catch (error) {
            console.log('⚠️  清理测试文件失败:', error.message);
        }

        console.log('\n🎉 测试完成！');
        console.log('💡 如果测试成功，您可以运行以下命令开始完整下载:');
        console.log('   cd dl_ziliao && node download.js');
        console.log('   或者: cd dl_ziliao && node batch-downloader.js');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error(error.stack);
    }
}

// 运行测试
if (require.main === module) {
    testDownloader().catch(console.error);
}

module.exports = testDownloader;
