import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/ActivityList.vue'),
    meta: {
      title: '普法答题活动'
    }
  },
  {
    path: '/activity/:id',
    name: 'ActivityDetail',
    component: () => import('@/views/ActivityDetail.vue'),
    meta: {
      title: '活动详情'
    }
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/views/SearchResults.vue'),
    meta: {
      title: '搜索结果'
    }
  },
  {
    path: '/no-results',
    name: 'NoResults',
    component: () => import('@/views/NoResults.vue'),
    meta: {
      title: '无查询结果'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 中国普法答题中奖查询系统`
  }
  next()
})

export default router
