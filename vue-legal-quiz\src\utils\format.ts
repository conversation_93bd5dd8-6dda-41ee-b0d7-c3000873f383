/**
 * 格式化工具函数
 */

/**
 * 格式化金额显示
 * 超过1万的金额显示为万元，保留1位小数
 * @param amount 金额数值
 * @returns 格式化后的金额字符串
 */
export function formatAmount(amount: number): string {
  if (amount >= 10000) {
    const wanAmount = amount / 10000
    // 如果是整数万，不显示小数点
    if (wanAmount % 1 === 0) {
      return `¥${wanAmount}万`
    } else {
      return `¥${wanAmount.toFixed(1)}万`
    }
  } else {
    return `¥${amount}`
  }
}

/**
 * 格式化手机号（中间4位用*号替换）
 * @param phone 原始手机号
 * @returns 格式化后的手机号
 */
export function formatPhone(phone: string): string {
  if (phone.length !== 11) {
    return phone
  }
  return phone.substring(0, 3) + '****' + phone.substring(7)
}

/**
 * 格式化时间显示
 * @param timeStr 时间字符串
 * @returns 格式化后的时间
 */
export function formatTime(timeStr: string): string {
  return timeStr.split(' ')[1] // 只显示时间部分
}

/**
 * 格式化日期显示
 * @param dateStr 日期字符串
 * @returns 格式化后的日期
 */
export function formatDate(dateStr: string): string {
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

/**
 * 格式化数字显示（添加千分位分隔符）
 * @param num 数字
 * @returns 格式化后的数字字符串
 */
export function formatNumber(num: number): string {
  return num.toLocaleString('zh-CN')
}
