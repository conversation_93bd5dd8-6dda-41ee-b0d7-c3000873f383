{"name": "vue-legal-quiz", "version": "1.0.0", "private": true, "type": "module", "description": "中国普法答题中奖查询系统", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "build:only": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@vueuse/core": "^10.5.0", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.8.10", "@vitejs/plugin-vue": "^4.4.1", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-vue": "^9.18.1", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "terser": "^5.40.0", "typescript": "~5.2.0", "vite": "^4.5.0", "vue-tsc": "^1.8.22"}}