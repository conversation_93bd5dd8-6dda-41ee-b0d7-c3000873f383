import axios from "axios"
import { load } from "cheerio"
import dayjs from "dayjs"
// import send from "../sendNotify.js"

// 中国银行公告地址
const ZGYH_NOTICE = "http://wzdig.pbc.gov.cn:8080/search/pcRender?pageId=fa445f64514c40c68b1c8ffe859c649e&ext=-siteId%3A3688005&ty=&dr=true&w=&f=&rp=&sr=score+desc&q=%E4%B8%AD%E5%9B%BD%E4%BA%BA%E6%B0%91%E9%93%B6%E8%A1%8C%E5%85%AC%E5%91%8A"
// 获取5天前时间
const sevenAgoTime = dayjs().subtract(5, 'd')

async function start() {
    let ret = await axios.get(ZGYH_NOTICE)
    const $ = load(ret.data)
    // 找到第一个公告连接
    for (let i = 0; i < 5; i++) {
        // 获取元素
        let ele = $('.news-style1').eq(i)
        // 获取公告链接
        let url = ele.find('.dates a').attr('href') as string;
        // 获取公告标题
        let atext = ele.find('.txtCon').text() as string;
        // 获取公告时间
        let date = ele.find('.dates span').eq(1).text() as string;
        // console.log(url);
        let isNJBI = await getNoticeContent(url)
        let formatDate = date.replace('年','-').replace('月','-').replace('日','')
        console.log('[ formatDate ] >', formatDate)
        if (isNJBI) {
            console.log('[ 发现纪念币 ] >\n', `公告名称：${atext.trim()} \n 公告链接：${url} \n 日期: ${date}`)
            if (dayjs(formatDate).isAfter(sevenAgoTime)) {
                // 发布的公告是否7天内发布
                console.log('[ 7天内发布 ] >')
                // await send.sendNotify('发现纪念币', `公告名称：${atext.trim()} \n 公告链接：${url} \n 日期: ${date}`);
            }else{
                console.log('[ 公告发布超过7天 ] >')
            }
        }
    }
}

async function getNoticeContent(url: string) {
    let ret = await axios.get(url)
    const $ = load(ret.data)
    /* let plength = $('#zoom p').length
    for (let i = 0; i < plength; i++) {
        let ptext = $('#zoom p').eq(i).text();
        if (ptext.indexOf('纪念币') > -1) {
            // 发现纪念币
            return true
        }
    } */
    let zoomhtml = $('#zoom').html() as string
    if (zoomhtml.indexOf('纪念币') > -1) {
        // 发现纪念币
        return true
    }
    return false
}
start()