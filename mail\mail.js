const nodemailer = require('nodemailer');
const dayjs = require('dayjs');
require('dayjs/locale/zh-cn');
const send = require('../sendNotify.js')

// 设置中文本地化
dayjs.locale('zh-cn');

/**
 * 邮件发送配置
 */
class EmailSender {
    constructor() {
        // 从环境变量获取邮箱配置，如果没有则使用默认值
        this.config = {
            sender: {
                email: process.env.SENDER_EMAIL || '<EMAIL>',
                password: process.env.SENDER_PASSWORD || 'QXUHd33mdrBSRpPz', // 163邮箱授权码
                name: process.env.SENDER_NAME || 'Wei Cracker'
            },
            recipient: {
                email: process.env.RECIPIENT_EMAIL || '<EMAIL>',
                name: process.env.RECIPIENT_NAME || 'Howard'
            },
            smtp: {
                host: 'smtp.163.com',
                port: 465,
                secure: true // 使用SSL
            }
        };

        this.transporter = null;
    }

    /**
     * 初始化邮件传输器
     */
    async initTransporter() {
        try {
            this.transporter = nodemailer.createTransport({
                host: this.config.smtp.host,
                port: this.config.smtp.port,
                secure: this.config.smtp.secure,
                auth: {
                    user: this.config.sender.email,
                    pass: this.config.sender.password
                }
            });

            // 验证连接
            await this.transporter.verify();
            console.log('✅ SMTP连接验证成功');
            return true;
        } catch (error) {
            console.error('❌ SMTP连接验证失败:', error.message);
            return false;
        }
    }

    /**
     * 生成邮件内容
     */
    generateEmailContent() {
        const now = dayjs();
        const currentDate = now.format('YYYY年MM月DD日 HH:mm:ss');
        const weekday = now.format('dddd');

        const subject = `定期邮件报告 - ${now.format('YYYY-MM-DD')}`;

        const htmlContent = `
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>邮件报告</title>
            <style>
                body {
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f5f5f5;
                }
                .container {
                    background-color: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .header {
                    text-align: center;
                    border-bottom: 2px solid #4CAF50;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }
                .header h1 {
                    color: #4CAF50;
                    margin: 0;
                }
                .info-section {
                    margin-bottom: 25px;
                }
                .info-item {
                    display: flex;
                    justify-content: space-between;
                    padding: 10px 0;
                    border-bottom: 1px solid #eee;
                }
                .info-label {
                    font-weight: bold;
                    color: #666;
                }
                .info-value {
                    color: #333;
                }
                .footer {
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #eee;
                    color: #888;
                    font-size: 14px;
                }
                .status-badge {
                    display: inline-block;
                    padding: 4px 12px;
                    background-color: #4CAF50;
                    color: white;
                    border-radius: 15px;
                    font-size: 12px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📧 定期邮件报告</h1>
                    <p>系统自动发送的状态报告</p>
                </div>

                <div class="info-section">
                    <h3>📅 时间信息</h3>
                    <div class="info-item">
                        <span class="info-label">发送时间:</span>
                        <span class="info-value">${currentDate}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">星期:</span>
                        <span class="info-value">${weekday}</span>
                    </div>
                </div>

                <div class="info-section">
                    <h3>👤 发送者信息</h3>
                    <div class="info-item">
                        <span class="info-label">发送者:</span>
                        <span class="info-value">${this.config.sender.name}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">邮箱:</span>
                        <span class="info-value">${this.config.sender.email}</span>
                    </div>
                </div>

                <div class="info-section">
                    <h3>📊 系统状态</h3>
                    <div class="info-item">
                        <span class="info-label">邮件系统:</span>
                        <span class="info-value"><span class="status-badge">正常运行</span></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">发送状态:</span>
                        <span class="info-value"><span class="status-badge">成功</span></span>
                    </div>
                </div>

                <div class="info-section">
                    <h3>📝 邮件目的</h3>
                    <p>这是一封自动发送的测试邮件，用于验证邮件发送功能是否正常工作。</p>
                    <p>如果您收到此邮件，说明邮件系统配置正确，可以正常发送邮件。</p>
                </div>

                <div class="footer">
                    <p>此邮件由 Node.js 自动发送系统生成</p>
                    <p>发送时间: ${currentDate}</p>
                </div>
            </div>
        </body>
        </html>
        `;

        const textContent = `
定期邮件报告 - ${now.format('YYYY-MM-DD')}

时间信息:
- 发送时间: ${currentDate}
- 星期: ${weekday}

发送者信息:
- 发送者: ${this.config.sender.name}
- 邮箱: ${this.config.sender.email}

系统状态:
- 邮件系统: 正常运行
- 发送状态: 成功

邮件目的:
这是一封自动发送的测试邮件，用于验证邮件发送功能是否正常工作。
如果您收到此邮件，说明邮件系统配置正确，可以正常发送邮件。

---
此邮件由 Node.js 自动发送系统生成
发送时间: ${currentDate}
        `;

        return {
            subject,
            html: htmlContent,
            text: textContent
        };
    }

    /**
     * 发送邮件
     */
    async sendEmail() {
        try {
            // 检查配置
            if (!this.config.sender.password) {
                throw new Error('请设置发送者邮箱授权码 (SENDER_PASSWORD 环境变量)');
            }

            // 初始化传输器
            const isConnected = await this.initTransporter();
            if (!isConnected) {
                throw new Error('无法连接到SMTP服务器');
            }

            // 生成邮件内容
            const emailContent = this.generateEmailContent();

            // 邮件选项
            const mailOptions = {
                from: {
                    name: this.config.sender.name,
                    address: this.config.sender.email
                },
                to: {
                    name: this.config.recipient.name,
                    address: this.config.recipient.email
                },
                subject: emailContent.subject,
                text: emailContent.text,
                html: emailContent.html
            };

            console.log('📧 正在发送邮件...');
            console.log(`发送者: ${this.config.sender.email}`);
            console.log(`接收者: ${this.config.recipient.email}`);
            console.log(`主题: ${emailContent.subject}`);

            // 发送邮件
            const info = await this.transporter.sendMail(mailOptions);

            console.log('✅ 邮件发送成功!');
            console.log(`消息ID: ${info.messageId}`);
            console.log(`发送时间: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`);
            await send.sendNotify('邮件发送成功', `消息ID: ${info.messageId} \n 发送时间: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`);

            return {
                success: true,
                messageId: info.messageId,
                info: info
            };

        } catch (error) {
            console.error('❌ 邮件发送失败:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 关闭连接
     */
    close() {
        if (this.transporter) {
            this.transporter.close();
            console.log('📪 SMTP连接已关闭');
        }
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 启动邮件发送程序...');
    console.log('='.repeat(50));

    const emailSender = new EmailSender();

    try {
        const result = await emailSender.sendEmail();

        if (result.success) {
            console.log('='.repeat(50));
            console.log('🎉 程序执行完成，邮件发送成功！');
        } else {
            console.log('='.repeat(50));
            console.log('💥 程序执行完成，但邮件发送失败！');
            process.exit(1);
        }
    } catch (error) {
        console.error('💥 程序执行出错:', error.message);
        process.exit(1);
    } finally {
        emailSender.close();
    }
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
    main();
}

// 导出EmailSender类供其他模块使用
module.exports = EmailSender;