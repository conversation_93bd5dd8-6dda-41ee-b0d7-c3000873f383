// node-schedule 每天0.0.1 直接执行 getDaan.js 后 再执行 main.py

const schedule = require('node-schedule');
const { exec } = require('child_process');
const path = require('path');

// 获取当前脚本所在目录
const scriptDir = __dirname;

// 定义要执行的脚本路径
const getDaanScript = path.join(scriptDir, 'getDaan.js');
const mainPyScript = path.join(scriptDir, 'main.py');

// 设置定时任务，每天 00:01 执行
schedule.scheduleJob('1 0 * * *', async () => {
  console.log(`[${new Date().toLocaleString()}] 开始执行自动发布任务...`);
  
  try {
    // 先执行 getDaan.js 获取答案
    console.log(`[${new Date().toLocaleString()}] 执行 getDaan.js 获取答案...`);
    await executeCommand(`node "${getDaanScript}"`);
    
    // 然后执行 main.py 发布笔记
    console.log(`[${new Date().toLocaleString()}] 执行 main.py 发布笔记...`);
    await executeCommand(`uv run "${mainPyScript}"`);
    
    console.log(`[${new Date().toLocaleString()}] 自动发布任务完成！`);
  } catch (error) {
    console.error(`[${new Date().toLocaleString()}] 执行任务出错:`, error);
  }
});

// 执行命令的函数
function executeCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`执行命令出错: ${error}`);
        console.error(`错误输出: ${stderr}`);
        reject(error);
        return;
      }
      
      console.log(`命令输出: ${stdout}`);
      resolve(stdout);
    });
  });
}

console.log(`[${new Date().toLocaleString()}] 小红书自动发布服务已启动，等待定时任务执行...`);




