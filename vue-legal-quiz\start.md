# 快速启动指南

## 问题解决

如果遇到 PostCSS 配置错误，已经修复了以下问题：

1. ✅ 添加了 `"type": "module"` 到 package.json
2. ✅ 修复了 PostCSS 和 Tailwind 配置文件格式
3. ✅ 修复了自定义 CSS 类名（将不存在的类名替换为标准 Tailwind 类名）

## 启动步骤

```bash
# 1. 进入项目目录
cd vue-legal-quiz

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev
```

## 修复的问题

### CSS 类名修复
- `rounded-12` → `rounded-xl`
- `rounded-16` → `rounded-2xl` 
- `rounded-20` → `rounded-3xl`
- `rounded-25` → `rounded-full`
- `w-15 h-15` → `w-16 h-16`
- `w-30 h-30` → `w-32 h-32`
- `py-15` → `py-16`

### 配置文件修复
- PostCSS 配置使用 ES 模块语法
- Tailwind 配置使用 ES 模块语法
- package.json 添加 `"type": "module"`

## 如果仍有问题

如果启动时仍有错误，请检查：

1. Node.js 版本 >= 16.0.0
2. npm 版本 >= 7.0.0
3. 确保所有依赖都已正确安装

## 项目特性

- ✅ Vue 3 + TypeScript
- ✅ Vite 快速构建
- ✅ Tailwind CSS 样式
- ✅ Pinia 状态管理
- ✅ Vue Router 路由
- ✅ 移动端响应式设计
- ✅ 小红书推广弹框
- ✅ 普法主题设计
