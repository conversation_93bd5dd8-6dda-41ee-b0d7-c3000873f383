#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const https = require('https');
const http = require('http');
const { URL } = require('url');

/**
 * 批量文件下载器
 * 从 ziliao.json 文件读取文件列表并批量下载
 */
class BatchDownloader {
    constructor(options = {}) {
        this.baseUrl = options.baseUrl || 'https://campus.chinaunicom.cn/training/image/';
        this.downloadDir = options.downloadDir || './downloads';
        this.maxConcurrent = options.maxConcurrent || 3;
        this.retryAttempts = options.retryAttempts || 3;
        this.retryDelay = options.retryDelay || 1000;
        
        this.stats = {
            total: 0,
            completed: 0,
            failed: 0,
            skipped: 0
        };
        
        this.activeDownloads = 0;
        this.downloadQueue = [];
    }

    /**
     * 读取并解析 JSON 文件
     */
    async loadFileList(jsonPath) {
        try {
            const data = await fs.readFile(jsonPath, 'utf8');
            const jsonData = JSON.parse(data);
            
            if (!jsonData.entity || !jsonData.entity.planLibList) {
                throw new Error('JSON 文件格式不正确：缺少 entity.planLibList');
            }
            
            return jsonData.entity.planLibList;
        } catch (error) {
            throw new Error(`读取 JSON 文件失败: ${error.message}`);
        }
    }

    /**
     * 确保目录存在
     */
    async ensureDir(dirPath) {
        try {
            await fs.access(dirPath);
        } catch {
            await fs.mkdir(dirPath, { recursive: true });
        }
    }

    /**
     * 获取文件扩展名
     */
    getFileExtension(url) {
        const pathname = new URL(url).pathname;
        return path.extname(pathname) || '.pdf'; // 默认为 PDF
    }

    /**
     * 生成安全的文件名
     */
    sanitizeFileName(fileName) {
        return fileName.replace(/[<>:"/\\|?*]/g, '_').trim();
    }

    /**
     * 下载单个文件
     */
    async downloadFile(fileInfo, attempt = 1) {
        const { libUrl, libName, libId } = fileInfo;
        const fullUrl = this.baseUrl + libUrl;
        const extension = this.getFileExtension(fullUrl);
        const safeFileName = this.sanitizeFileName(libName) + extension;
        const localPath = path.join(this.downloadDir, safeFileName);

        try {
            // 检查文件是否已存在
            try {
                await fs.access(localPath);
                console.log(`⏭️  跳过已存在的文件: ${safeFileName}`);
                this.stats.skipped++;
                return { success: true, skipped: true };
            } catch {
                // 文件不存在，继续下载
            }

            console.log(`📥 开始下载 (${attempt}/${this.retryAttempts}): ${safeFileName}`);
            
            await this.downloadWithProgress(fullUrl, localPath, safeFileName);
            
            console.log(`✅ 下载完成: ${safeFileName}`);
            this.stats.completed++;
            return { success: true };
            
        } catch (error) {
            console.error(`❌ 下载失败 (${attempt}/${this.retryAttempts}): ${safeFileName} - ${error.message}`);
            
            if (attempt < this.retryAttempts) {
                console.log(`🔄 等待 ${this.retryDelay}ms 后重试...`);
                await this.sleep(this.retryDelay);
                return this.downloadFile(fileInfo, attempt + 1);
            } else {
                this.stats.failed++;
                return { success: false, error: error.message };
            }
        }
    }

    /**
     * 带进度显示的下载
     */
    downloadWithProgress(url, localPath, fileName) {
        return new Promise((resolve, reject) => {
            const protocol = url.startsWith('https:') ? https : http;
            
            const request = protocol.get(url, (response) => {
                if (response.statusCode === 302 || response.statusCode === 301) {
                    // 处理重定向
                    return this.downloadWithProgress(response.headers.location, localPath, fileName)
                        .then(resolve)
                        .catch(reject);
                }
                
                if (response.statusCode !== 200) {
                    reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
                    return;
                }

                const totalSize = parseInt(response.headers['content-length'], 10);
                let downloadedSize = 0;
                
                const fileStream = require('fs').createWriteStream(localPath);
                
                response.on('data', (chunk) => {
                    downloadedSize += chunk.length;
                    if (totalSize) {
                        const progress = ((downloadedSize / totalSize) * 100).toFixed(1);
                        process.stdout.write(`\r📊 ${fileName}: ${progress}% (${this.formatBytes(downloadedSize)}/${this.formatBytes(totalSize)})`);
                    }
                });
                
                response.on('end', () => {
                    console.log(''); // 换行
                });
                
                response.pipe(fileStream);
                
                fileStream.on('finish', () => {
                    fileStream.close();
                    resolve();
                });
                
                fileStream.on('error', (error) => {
                    fileStream.close();
                    fs.unlink(localPath).catch(() => {}); // 删除不完整的文件
                    reject(error);
                });
            });
            
            request.on('error', reject);
            request.setTimeout(30000, () => {
                request.destroy();
                reject(new Error('下载超时'));
            });
        });
    }

    /**
     * 格式化字节大小
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 延迟函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 并发控制的下载队列处理
     */
    async processDownloadQueue() {
        while (this.downloadQueue.length > 0 && this.activeDownloads < this.maxConcurrent) {
            const fileInfo = this.downloadQueue.shift();
            this.activeDownloads++;
            
            this.downloadFile(fileInfo)
                .finally(() => {
                    this.activeDownloads--;
                    this.processDownloadQueue(); // 继续处理队列
                });
        }
    }

    /**
     * 开始批量下载
     */
    async startDownload(jsonPath) {
        try {
            console.log('🚀 开始批量下载任务...');
            console.log(`📂 JSON 文件: ${jsonPath}`);
            console.log(`📁 下载目录: ${this.downloadDir}`);
            console.log(`🔄 最大并发数: ${this.maxConcurrent}`);
            console.log('');

            // 确保下载目录存在
            await this.ensureDir(this.downloadDir);

            // 加载文件列表
            const fileList = await this.loadFileList(jsonPath);
            this.stats.total = fileList.length;
            
            console.log(`📋 找到 ${this.stats.total} 个文件待下载`);
            console.log('');

            // 添加到下载队列
            this.downloadQueue = [...fileList];
            
            // 开始处理下载队列
            await this.processDownloadQueue();
            
            // 等待所有下载完成
            while (this.activeDownloads > 0) {
                await this.sleep(100);
            }

            // 显示统计信息
            this.showStats();
            
        } catch (error) {
            console.error(`❌ 批量下载失败: ${error.message}`);
            process.exit(1);
        }
    }

    /**
     * 显示下载统计信息
     */
    showStats() {
        console.log('\n📊 下载统计:');
        console.log(`总文件数: ${this.stats.total}`);
        console.log(`✅ 成功下载: ${this.stats.completed}`);
        console.log(`⏭️  跳过文件: ${this.stats.skipped}`);
        console.log(`❌ 下载失败: ${this.stats.failed}`);
        
        if (this.stats.failed === 0) {
            console.log('\n🎉 所有文件下载完成！');
        } else {
            console.log(`\n⚠️  有 ${this.stats.failed} 个文件下载失败，请检查网络连接或文件URL`);
        }
    }
}

/**
 * 加载配置文件
 */
async function loadConfig() {
    try {
        const configData = await fs.readFile(path.join(__dirname, 'downloader-config.json'), 'utf8');
        return JSON.parse(configData);
    } catch (error) {
        console.log('⚠️  未找到配置文件，使用默认配置');
        return {
            baseUrl: 'https://campus.chinaunicom.cn/training/image/',
            downloadDir: './downloads',
            maxConcurrent: 3,
            retryAttempts: 3,
            retryDelay: 1000
        };
    }
}

// 主程序
async function main() {
    const args = process.argv.slice(2);
    const jsonPath = args[0] || path.join(__dirname, 'ziliao.json');
    
    // 加载配置
    const config = await loadConfig();
    console.log('⚙️  加载配置:', {
        downloadDir: config.downloadDir,
        maxConcurrent: config.maxConcurrent,
        retryAttempts: config.retryAttempts
    });
    
    const downloader = new BatchDownloader(config);
    
    await downloader.startDownload(jsonPath);
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(console.error);
}

module.exports = BatchDownloader;
