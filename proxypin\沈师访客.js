// 在请求到达服务器之前,调用此函数,您可以在此处修改请求数据
// e.g. Add/Update/Remove：Queries、Headers、Body


async function onRequest(context, request) {
    console.log(request.url);
    //URL queries
    //request.queries["name"] = "value";
    //Update or add Header
    //request.headers["X-New-Headers"] = "My-Value";
    
    // Update Body use fetch API request，具体文档可网上搜索fetch API
    //request.body = await fetch('https://www.baidu.com/').then(response => response.text());
    return request;
  }
  
  //You can modify the Response Data here before it goes to the client
  async function onResponse(context, request, response) {
      var listRes = {"total":1,"rows":[{"searchValue":null,"createBy":null,"createTime":"2025-05-18 17:10:56","updateBy":null,"updateTime":null,"remark":null,"params":{},"id":64265,"year":"2025","pic":"https://synu-1331963644.cos.ap-beijing.myqcloud.com/auto-login-v2/dhd_zhcp/2025/5/18/8482f5df-2f83-4056-8e68-fcb54604f7e7.jpeg","wId":null,"name":"王洋达","isChina":"0","idCard":"211324198707069898","realName":null,"info":null,"phone":"15205423216","dept":"辽宁大学","plate":"辽AD3526","hQr":"","sink":"国际交流合作处","receiver":"毛老师","receiverPhone":"15042151234","reason":"拜访，交流合作","beginTime":"2025-05-18T00:00:00.000+08:00","st":null,"ed":null,"endTime":"2025-05-18T00:00:00.000+08:00","tQr":null,"audit":null,"isAudit":"1","userType":"邀约人员","key":"NjQyNjU=","auditTime":null,"aTime":null,"auditDto":null,"openid":"oppA56ziYoJ-9ze1Oi4YcKCDixTU","appId":null,"subscribe":null,"nickname":"********","sex":null,"city":null,"country":null,"province":null,"language":null,"headimgurl":null,"subscribeTime":null,"unionid":"ol6Rg54IMW81r85a2pVQ9XryG-vY","groupid":null,"tagidList":null,"subscribeScene":null,"qrScene":null,"qrSceneStr":null,"delBy":null,"delTime":null,"isDel":"0"}],"code":200,"msg":"查询成功"}
  var infoRes = {"msg":"操作成功","code":200,"data":{"searchValue":null,"createBy":null,"createTime":"2025-05-18 17:10:56","updateBy":null,"updateTime":null,"remark":null,"params":{},"id":64265,"year":"2025","pic":"https://synu-1331963644.cos.ap-beijing.myqcloud.com/auto-login-v2/dhd_zhcp/2025/5/18/8482f5df-2f83-4056-8e68-fcb54604f7e7.jpeg","wId":null,"name":"王洋达","isChina":"0","idCard":"211324198707069898","realName":null,"info":null,"phone":"15205423216","dept":"辽宁大学","plate":"辽AD3526","hQr":"","sink":"国际交流合作处","receiver":"毛老师","receiverPhone":"15042151234","reason":"拜访，交流合作","beginTime":"2025-05-18T00:00:00.000+08:00","st":null,"ed":null,"endTime":"2025-05-18T00:00:00.000+08:00","tQr":null,"audit":null,"isAudit":"1","userType":"邀约人员","key":null,"auditTime":"2025-05-18T00:00:00.000+08:00","aTime":null,"auditDto":null,"openid":"oppA56ziYoJ-9ze1Oi4YcKCDixTU","appId":null,"subscribe":null,"nickname":"********","sex":null,"city":null,"country":null,"province":null,"language":null,"headimgurl":null,"subscribeTime":null,"unionid":"ol6Rg54IMW81r85a2pVQ9XryG-vY","groupid":null,"tagidList":null,"subscribeScene":null,"qrScene":null,"qrSceneStr":null,"delBy":null,"delTime":null,"isDel":"0"}}
  function getCurDate() {
    // 获取当前时间（本地时区）
    var now = new Date();
  
    // 提取年月日，并补零（确保两位数格式）
    var year = now.getFullYear();
    var month = String(now.getMonth() + 1).padStart(2, "0"); // 0-11 → 补零
    var day = String(now.getDate()).padStart(2, "0"); // 1-31 → 补零
  
    // 拼接成目标格式
    var formattedDate = `${year}-${month}-${day}`;
    console.log(formattedDate); // 示例输出: 2025-05-19
    return formattedDate;
  }
     //Update or add Header
    // response.headers["Name"] = "Value";
    // response.statusCode = 200;
  
    //var body = JSON.parse(response.body);
    //body['key'] = "value";
    //response.body = JSON.stringify(body);
  
    if (request.url.includes("info")) {
      infoRes.data.beginTime = getCurDate()+'T00:00:00+08:00';
      infoRes.data.endTime = getCurDate()+'T00:00:00+08:00';
      infoRes.data.audit = "1";
      infoRes.data.auditTime = getCurDate()+' 08:10:56';
      infoRes.data.aTime = getCurDate()+' 08:10:56';
      response.body = JSON.stringify(infoRes);
    } else if (request.url.includes("list")) {
      listRes.rows[0].beginTime = getCurDate()+'T00:00:00+08:00';
      listRes.rows[0].endTime = getCurDate()+'T00:00:00+08:00';
      listRes.rows[0].audit = "1";
      listRes.rows[0].auditTime = getCurDate()+' 08:10:56';
      listRes.rows[0].aTime = getCurDate()+' 08:10:56';
      response.body = JSON.stringify(listRes);
    }
  
    return response;
  }